import { Test, TestingModule } from '@nestjs/testing';
import { RecoveredValueUseCase } from '@business-base/application/use-cases/recovered-value.use-case';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { StatsDataSource } from '@business-base/application/dto/customer-preferences.dto';

describe('RecoveredValueUseCase - Dynamic Statistical Fields Extraction', () => {
  let useCase: RecoveredValueUseCase;
  let customerPreferencesAdapter: jest.Mocked<CustomerPreferencesPort>;
  let portfolioItemAdapter: jest.Mocked<PortfolioItemPort>;
  let portfolioItemCustomDataAdapter: jest.Mocked<PortfolioItemCustomDataPort>;
  let middlewareResponseOutputAdapter: jest.Mocked<MiddlewareResponseOutputPort>;
  let collectCashStatsAdapter: jest.Mocked<CollectCashStatsPort>;
  let portfolioAdapter: jest.Mocked<PortfolioPort>;

  beforeEach(async () => {
    const mockCustomerPreferencesAdapter = {
      getById: jest.fn(),
    };

    const mockPortfolioItemAdapter = {
      get: jest.fn(),
    };

    const mockPortfolioItemCustomDataAdapter = {
      getByPortfolioItemId: jest.fn(),
    };

    const mockMiddlewareResponseOutputAdapter = {
      getByPortfolioItemId: jest.fn(),
    };

    const mockCollectCashStatsAdapter = {
      create: jest.fn(),
    };

    const mockPortfolioAdapter = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecoveredValueUseCase,
        { provide: 'CollectCashStatsPort', useValue: mockCollectCashStatsAdapter },
        { provide: 'CustomerPreferencesPort', useValue: mockCustomerPreferencesAdapter },
        { provide: 'PortfolioItemPort', useValue: mockPortfolioItemAdapter },
        { provide: 'PortfolioItemCustomDataPort', useValue: mockPortfolioItemCustomDataAdapter },
        { provide: 'MiddlewareResponseOutputPort', useValue: mockMiddlewareResponseOutputAdapter },
        { provide: 'PortfolioPort', useValue: mockPortfolioAdapter },
      ],
    }).compile();

    useCase = module.get<RecoveredValueUseCase>(RecoveredValueUseCase);
    customerPreferencesAdapter = module.get('CustomerPreferencesPort');
    portfolioItemAdapter = module.get('PortfolioItemPort');
    portfolioItemCustomDataAdapter = module.get('PortfolioItemCustomDataPort');
    middlewareResponseOutputAdapter = module.get('MiddlewareResponseOutputPort');
    collectCashStatsAdapter = module.get('CollectCashStatsPort');
    portfolioAdapter = module.get('PortfolioPort');
  });

  describe('extractAndStoreRecoveredValue with dynamic statsConfig', () => {
    it('should extract multiple statistical fields from workflow-specific configuration', async () => {
      const customerId = 'customer-123';
      const portfolioId = 'portfolio-456';
      const portfolioItemId = 'item-789';
      const workflowId = 'matrix-energia-negociador-divida-v1';

      // Mock customer preferences with dynamic statsConfig
      const customerPreferences = {
        portfolio: {
          statsConfig: [
            {
              workflowId: 'matrix-energia-negociador-divida-v1',
              recoveredValueFrom: {
                source: StatsDataSource.MIDDLEWARE,
                path: '[\'deal-info\'].VALOR_RECUPERADO',
              },
              currentDebit: {
                source: StatsDataSource.MIDDLEWARE,
                path: '[\'valor-info\'].VALOR_ORIGINAL',
              },
              originalDebt: {
                source: StatsDataSource.CUSTOM_DATA,
                path: 'VALOR_ORIGINAL_DIVIDA',
              },
            },
          ],
        },
      };

      // Mock portfolio item
      const portfolioItem = {
        id: portfolioItemId,
        customDataId: 'custom-data-123',
        middlewareResponseOutputId: 'middleware-456',
      };

      // Mock middleware response with nested data
      const middlewareResponse = {
        middlewareResponse: {
          'deal-info': {
            VALOR_RECUPERADO: '2750.50',
          },
          'valor-info': {
            VALOR_ORIGINAL: '5000.00',
          },
        },
      };

      // Mock custom data
      const customData = {
        customData: {
          VALOR_ORIGINAL_DIVIDA: '4500.75',
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(customerPreferences);
      portfolioItemAdapter.get.mockResolvedValue(portfolioItem);
      middlewareResponseOutputAdapter.getByPortfolioItemId.mockResolvedValue(middlewareResponse);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(customData);
      collectCashStatsAdapter.create.mockResolvedValue(undefined);

      await useCase.extractAndStoreRecoveredValue(customerId, portfolioId, portfolioItemId, workflowId);

      // Verify that CollectCashStatsEntity was created with extracted values
      expect(collectCashStatsAdapter.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customerId,
          portfolioId,
          portfolioItemId,
          workflowId,
          dealValue: 2750.5, // From recoveredValueFrom
          currentDebit: 5000.0, // From currentDebit
          originalDebt: 4500.75, // From originalDebt
          installments: 1,
        }),
      );
    });

    it('should handle missing statistical field configurations gracefully', async () => {
      const customerId = 'customer-123';
      const portfolioId = 'portfolio-456';
      const portfolioItemId = 'item-789';
      const workflowId = 'matrix-energia-negociador-divida-v2';

      // Mock customer preferences with partial statsConfig (only recoveredValueFrom)
      const customerPreferences = {
        portfolio: {
          statsConfig: [
            {
              workflowId: 'matrix-energia-negociador-divida-v2',
              recoveredValueFrom: {
                source: StatsDataSource.CUSTOM_DATA,
                path: 'VALOR_RECEBIDO',
              },
              // No currentDebit or originalDebt configured
            },
          ],
        },
      };

      const portfolioItem = {
        id: portfolioItemId,
        customDataId: 'custom-data-123',
        middlewareResponseOutputId: null,
      };

      const customData = {
        customData: {
          VALOR_RECEBIDO: '1500.25',
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(customerPreferences);
      portfolioItemAdapter.get.mockResolvedValue(portfolioItem);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(customData);
      collectCashStatsAdapter.create.mockResolvedValue(undefined);

      await useCase.extractAndStoreRecoveredValue(customerId, portfolioId, portfolioItemId, workflowId);

      // Verify that CollectCashStatsEntity was created with defaults for missing fields
      expect(collectCashStatsAdapter.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customerId,
          portfolioId,
          portfolioItemId,
          workflowId,
          dealValue: 1500.25, // From recoveredValueFrom
          currentDebit: 0, // Default value (not configured)
          originalDebt: 0, // Default value (not configured)
          installments: 1,
        }),
      );
    });

    it('should fall back to legacy recoveredValueFrom configuration', async () => {
      const customerId = 'customer-123';
      const portfolioId = 'portfolio-456';
      const portfolioItemId = 'item-789';
      const workflowId = 'legacy-workflow';

      // Mock customer preferences with legacy configuration (no statsConfig)
      const customerPreferences = {
        portfolio: {
          recoveredValueFrom: {
            source: StatsDataSource.CUSTOM_DATA,
            path: 'VALOR_RECEBIDO',
          },
        },
      };

      const portfolioItem = {
        id: portfolioItemId,
        customDataId: 'custom-data-123',
        middlewareResponseOutputId: null,
      };

      const customData = {
        customData: {
          VALOR_RECEBIDO: '3000.00',
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(customerPreferences);
      portfolioItemAdapter.get.mockResolvedValue(portfolioItem);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(customData);
      collectCashStatsAdapter.create.mockResolvedValue(undefined);

      await useCase.extractAndStoreRecoveredValue(customerId, portfolioId, portfolioItemId, workflowId);

      // Verify that CollectCashStatsEntity was created with legacy configuration
      expect(collectCashStatsAdapter.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customerId,
          portfolioId,
          portfolioItemId,
          workflowId,
          dealValue: 3000.0, // From legacy recoveredValueFrom
          currentDebit: 0, // Default value (legacy doesn't support)
          originalDebt: 0, // Default value (legacy doesn't support)
          installments: 1,
        }),
      );
    });

    it('should skip creation when no recovered value is found', async () => {
      const customerId = 'customer-123';
      const portfolioId = 'portfolio-456';
      const portfolioItemId = 'item-789';
      const workflowId = 'no-config-workflow';

      // Mock customer preferences with no configuration
      const customerPreferences = {
        portfolio: {},
      };

      customerPreferencesAdapter.getById.mockResolvedValue(customerPreferences);

      await useCase.extractAndStoreRecoveredValue(customerId, portfolioId, portfolioItemId, workflowId);

      // Verify that no CollectCashStatsEntity was created
      expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
    });
  });
});
