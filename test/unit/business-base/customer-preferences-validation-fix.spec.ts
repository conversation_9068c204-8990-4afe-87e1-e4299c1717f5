import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  PortfolioPreferencesDto,
  StatsConfigDto,
  StatsDataSource,
} from '@business-base/application/dto/customer-preferences.dto';

describe('Customer Preferences Validation Fix', () => {
  describe('StatsConfigDto Validation with Whitelist', () => {
    it('should pass validation with explicit recoveredValueFrom and currentDebit properties', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'matrix-energia-negociador-divida-v1',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        currentDebit: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'valor-info\'].VALOR_ORIGINAL',
        },
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.workflowId).toBe('matrix-energia-negociador-divida-v1');
      expect(dto.recoveredValueFrom).toBeDefined();
      expect(dto.recoveredValueFrom!.source).toBe(StatsDataSource.MIDDLEWARE);
      expect(dto.currentDebit).toBeDefined();
      expect(dto.currentDebit!.source).toBe(StatsDataSource.MIDDLEWARE);
    });

    it('should pass validation with all three explicit properties', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'matrix-energia-negociador-divida-v2',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        currentDebit: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_CORRIGIDO',
        },
        originalDebt: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_ORIGINAL',
        },
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.workflowId).toBe('matrix-energia-negociador-divida-v2');
      expect(dto.recoveredValueFrom).toBeDefined();
      expect(dto.currentDebit).toBeDefined();
      expect(dto.originalDebt).toBeDefined();
    });

    it('should pass validation with only workflowId (minimal configuration)', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'minimal-workflow',
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.workflowId).toBe('minimal-workflow');
      expect(dto.recoveredValueFrom).toBeUndefined();
      expect(dto.currentDebit).toBeUndefined();
      expect(dto.originalDebt).toBeUndefined();
    });

    it('should reject invalid source in recoveredValueFrom', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'test-workflow',
        recoveredValueFrom: {
          source: 'invalidSource',
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('recoveredValueFrom');
    });

    it('should reject empty path in currentDebit', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'test-workflow',
        currentDebit: {
          source: StatsDataSource.CUSTOM_DATA,
          path: '',
        },
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('currentDebit');
    });
  });

  describe('PortfolioPreferencesDto with StatsConfig Array', () => {
    it('should pass validation with statsConfig array containing explicit properties', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'matrix-energia-negociador-divida-v1',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'valor-info\'].VALOR_ORIGINAL',
            },
          },
          {
            workflowId: 'matrix-energia-negociador-divida-v2',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: StatsDataSource.CUSTOM_DATA,
              path: 'VALOR_CORRIGIDO',
            },
            originalDebt: {
              source: StatsDataSource.CUSTOM_DATA,
              path: 'VALOR_ORIGINAL',
            },
          },
        ],
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(2);
      expect(dto.statsConfig![0].workflowId).toBe('matrix-energia-negociador-divida-v1');
      expect(dto.statsConfig![0].recoveredValueFrom).toBeDefined();
      expect(dto.statsConfig![0].currentDebit).toBeDefined();
      expect(dto.statsConfig![1].originalDebt).toBeDefined();
    });

    it('should pass validation with mixed explicit and legacy configuration', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'new-workflow',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
          },
        ],
        recoveredValueFrom: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_RECEBIDO',
        },
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(1);
      expect(dto.recoveredValueFrom).toBeDefined();
      expect(dto.recoveredValueFrom!.source).toBe(StatsDataSource.CUSTOM_DATA);
    });

    it('should reject invalid statsConfig array items', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow',
        timezoneUTC: '-3',
        statsConfig: [
          {
            // Missing workflowId
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
          },
        ],
      });

      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('YAML Configuration Compatibility', () => {
    it('should handle the exact YAML structure from customer-preferences.yml', async () => {
      const yamlLikeConfig = {
        defaultWorkflowId: 'default-workflow',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'matrix-energia-negociador-divida-v1',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'middleware',
              path: '[\'valor-info\'].VALOR_ORIGINAL',
            },
          },
          {
            workflowId: 'matrix-energia-negociador-divida-v2',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'customData',
              path: 'VALOR_CORRIGIDO',
            },
          },
        ],
      };

      const dto = plainToClass(PortfolioPreferencesDto, yamlLikeConfig);
      const errors = await validate(dto, {
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(2);
      expect(dto.statsConfig![0].recoveredValueFrom!.source).toBe(StatsDataSource.MIDDLEWARE);
      expect(dto.statsConfig![1].currentDebit!.source).toBe(StatsDataSource.CUSTOM_DATA);
    });
  });
});
