import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import {
  StatsConfigDto,
  StatsFieldConfigDto,
  StatsDataSource,
  PortfolioPreferencesDto,
} from '@business-base/application/dto/customer-preferences.dto';

describe('StatsConfig DTO Validation', () => {
  describe('StatsFieldConfigDto', () => {
    it('should validate a valid stats field config', async () => {
      const dto = plainToClass(StatsFieldConfigDto, {
        source: StatsDataSource.MIDDLEWARE,
        path: '[\'deal-info\'].VALOR_RECUPERADO',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should reject invalid source', async () => {
      const dto = plainToClass(StatsFieldConfigDto, {
        source: 'invalidSource',
        path: '[\'deal-info\'].VALOR_RECUPERADO',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toHaveProperty('isEnum');
    });

    it('should reject empty path', async () => {
      const dto = plainToClass(StatsFieldConfigDto, {
        source: StatsDataSource.CUSTOM_DATA,
        path: '',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toHaveProperty('isNotEmpty');
    });
  });

  describe('StatsConfigDto', () => {
    it('should validate a complete stats config', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'matrix-energia-negociador-divida-v1',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        currentDebit: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_CORRIGIDO',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate minimal stats config with only workflowId', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'matrix-energia-negociador-divida-v2',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should support dynamic properties', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: 'test-workflow',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        customField: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'CUSTOM_VALUE',
        },
        anotherField: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'other-info\'].OTHER_VALUE',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.customField).toBeDefined();
      expect(dto.anotherField).toBeDefined();
    });

    it('should reject empty workflowId', async () => {
      const dto = plainToClass(StatsConfigDto, {
        workflowId: '',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].constraints).toHaveProperty('isNotEmpty');
    });
  });

  describe('PortfolioPreferencesDto with statsConfig', () => {
    it('should validate portfolio preferences with statsConfig array', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow-id',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'matrix-energia-negociador-divida-v1',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'valor-info\'].VALOR_ORIGINAL',
            },
          },
          {
            workflowId: 'matrix-energia-negociador-divida-v2',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: StatsDataSource.CUSTOM_DATA,
              path: 'VALOR_CORRIGIDO',
            },
          },
        ],
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(2);
    });

    it('should validate portfolio preferences with legacy recoveredValueFrom', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow-id',
        timezoneUTC: '-3',
        recoveredValueFrom: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_RECEBIDO',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.recoveredValueFrom).toBeDefined();
    });

    it('should validate portfolio preferences with both statsConfig and legacy recoveredValueFrom', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow-id',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'new-workflow',
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
          },
        ],
        recoveredValueFrom: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_RECEBIDO',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(1);
      expect(dto.recoveredValueFrom).toBeDefined();
    });

    it('should reject invalid statsConfig array items', async () => {
      const dto = plainToClass(PortfolioPreferencesDto, {
        defaultWorkflowId: 'default-workflow-id',
        timezoneUTC: '-3',
        statsConfig: [
          {
            // Missing workflowId
            recoveredValueFrom: {
              source: StatsDataSource.MIDDLEWARE,
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
          },
        ],
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('YAML Configuration Support', () => {
    it('should support the YAML structure from customer-preferences.yml', async () => {
      const yamlLikeConfig = {
        defaultWorkflowId: 'default-workflow-id',
        timezoneUTC: '-3',
        statsConfig: [
          {
            workflowId: 'matrix-energia-negociador-divida-v1',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'middleware',
              path: '[\'valor-info\'].VALOR_ORIGINAL',
            },
          },
          {
            workflowId: 'matrix-energia-negociador-divida-v2',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'customData',
              path: 'VALOR_CORRIGIDO',
            },
          },
        ],
      };

      const dto = plainToClass(PortfolioPreferencesDto, yamlLikeConfig);
      const errors = await validate(dto);
      
      expect(errors).toHaveLength(0);
      expect(dto.statsConfig).toHaveLength(2);
      expect(dto.statsConfig![0].workflowId).toBe('matrix-energia-negociador-divida-v1');
      expect(dto.statsConfig![0].recoveredValueFrom!.source).toBe(StatsDataSource.MIDDLEWARE);
      expect(dto.statsConfig![1].currentDebit!.source).toBe(StatsDataSource.CUSTOM_DATA);
    });
  });
});
