import { StatsConfigDto, StatsFieldConfigDto, StatsDataSource } from '@business-base/application/dto/customer-preferences.dto';
import { StatsConfig, StatsFieldConfig } from '@business-base/domain/entities/customer-preferences.entity';

describe('StatsConfig Dynamic Properties', () => {
  describe('StatsConfigDto Dynamic Access', () => {
    it('should allow dynamic property access for statistical fields', () => {
      const dto = new StatsConfigDto('test-workflow-id');
      
      // Assign dynamic properties
      dto['recoveredValueFrom'] = new StatsFieldConfigDto(StatsDataSource.MIDDLEWARE, '[\'deal-info\'].VALOR_RECUPERADO');
      dto['currentDebit'] = new StatsFieldConfigDto(StatsDataSource.CUSTOM_DATA, 'VALOR_CORRIGIDO');
      dto['originalDebt'] = new StatsFieldConfigDto(StatsDataSource.CUSTOM_DATA, 'VALOR_ORIGINAL');
      dto['customMetric'] = new StatsFieldConfigDto(StatsDataSource.MIDDLEWARE, '[\'custom-info\'].CUSTOM_VALUE');

      // Verify workflowId is accessible normally
      expect(dto.workflowId).toBe('test-workflow-id');

      // Verify dynamic properties are accessible via bracket notation
      expect(dto['recoveredValueFrom']).toBeDefined();
      expect(dto['recoveredValueFrom'].source).toBe(StatsDataSource.MIDDLEWARE);
      expect(dto['recoveredValueFrom'].path).toBe('[\'deal-info\'].VALOR_RECUPERADO');

      expect(dto['currentDebit']).toBeDefined();
      expect(dto['currentDebit'].source).toBe(StatsDataSource.CUSTOM_DATA);
      expect(dto['currentDebit'].path).toBe('VALOR_CORRIGIDO');

      expect(dto['originalDebt']).toBeDefined();
      expect(dto['customMetric']).toBeDefined();
    });

    it('should support object assignment for dynamic properties', () => {
      const configData = {
        workflowId: 'matrix-energia-negociador-divida-v1',
        recoveredValueFrom: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        currentDebit: {
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'valor-info\'].VALOR_ORIGINAL',
        },
        paymentTerms: {
          source: StatsDataSource.CUSTOM_DATA,
          path: 'PRAZO_PAGAMENTO',
        },
      };

      const dto = new StatsConfigDto(configData.workflowId, configData);

      expect(dto.workflowId).toBe('matrix-energia-negociador-divida-v1');
      expect(dto['recoveredValueFrom']).toBeDefined();
      expect(dto['currentDebit']).toBeDefined();
      expect(dto['paymentTerms']).toBeDefined();
      expect(dto['paymentTerms'].path).toBe('PRAZO_PAGAMENTO');
    });

    it('should handle YAML-like configuration structure', () => {
      const yamlConfig = {
        workflowId: 'matrix-energia-negociador-divida-v2',
        recoveredValueFrom: {
          source: 'middleware',
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
        currentDebit: {
          source: 'customData',
          path: 'VALOR_CORRIGIDO',
        },
      };

      const dto = new StatsConfigDto(yamlConfig.workflowId, yamlConfig);

      expect(dto.workflowId).toBe('matrix-energia-negociador-divida-v2');
      expect(dto['recoveredValueFrom']).toBeDefined();
      expect(dto['recoveredValueFrom'].source).toBe('middleware');
      expect(dto['currentDebit']).toBeDefined();
      expect(dto['currentDebit'].source).toBe('customData');
    });
  });

  describe('StatsConfig Domain Entity Dynamic Access', () => {
    it('should allow dynamic property access in domain entity', () => {
      const entity = new StatsConfig({
        workflowId: 'test-workflow',
        recoveredValueFrom: new StatsFieldConfig({
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        }),
        currentDebit: new StatsFieldConfig({
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_CORRIGIDO',
        }),
      });

      expect(entity.workflowId).toBe('test-workflow');
      expect(entity['recoveredValueFrom']).toBeDefined();
      expect(entity['recoveredValueFrom'].source).toBe(StatsDataSource.MIDDLEWARE);
      expect(entity['currentDebit']).toBeDefined();
      expect(entity['currentDebit'].source).toBe(StatsDataSource.CUSTOM_DATA);
    });

    it('should support arbitrary statistical field names', () => {
      const entity = new StatsConfig({
        workflowId: 'flexible-workflow',
        totalAmount: new StatsFieldConfig({
          source: StatsDataSource.CUSTOM_DATA,
          path: 'VALOR_TOTAL',
        }),
        discountRate: new StatsFieldConfig({
          source: StatsDataSource.MIDDLEWARE,
          path: '[\'discount-info\'].TAXA_DESCONTO',
        }),
        negotiationDate: new StatsFieldConfig({
          source: StatsDataSource.CUSTOM_DATA,
          path: 'DATA_NEGOCIACAO',
        }),
      });

      expect(entity.workflowId).toBe('flexible-workflow');
      expect(entity['totalAmount']).toBeDefined();
      expect(entity['discountRate']).toBeDefined();
      expect(entity['negotiationDate']).toBeDefined();
      expect(entity['totalAmount'].path).toBe('VALOR_TOTAL');
      expect(entity['discountRate'].source).toBe(StatsDataSource.MIDDLEWARE);
    });
  });

  describe('Type Safety with Dynamic Properties', () => {
    it('should maintain type safety for workflowId', () => {
      const dto = new StatsConfigDto('typed-workflow-id');
      
      // workflowId should be typed as string
      expect(typeof dto.workflowId).toBe('string');
      expect(dto.workflowId).toBe('typed-workflow-id');
    });

    it('should allow StatsFieldConfigDto or string types for dynamic properties', () => {
      const dto = new StatsConfigDto('test-workflow');
      
      // Should accept StatsFieldConfigDto
      dto['recoveredValue'] = new StatsFieldConfigDto(StatsDataSource.MIDDLEWARE, '[\'deal\'].VALUE');
      expect(dto['recoveredValue']).toBeInstanceOf(StatsFieldConfigDto);
      
      // Should also accept string (per the index signature)
      dto['simpleField'] = 'simple-string-value';
      expect(typeof dto['simpleField']).toBe('string');
    });
  });

  describe('Integration with RecoveredValueUseCase Pattern', () => {
    it('should support the pattern used by getRecoveredValueConfig helper', () => {
      // Simulate the structure that would be used by the RecoveredValueUseCase
      const customerPreferences = {
        portfolio: {
          statsConfig: [
            {
              workflowId: 'matrix-energia-negociador-divida-v1',
              recoveredValueFrom: {
                source: StatsDataSource.MIDDLEWARE,
                path: '[\'deal-info\'].VALOR_RECUPERADO',
              },
              currentDebit: {
                source: StatsDataSource.MIDDLEWARE,
                path: '[\'valor-info\'].VALOR_ORIGINAL',
              },
            },
            {
              workflowId: 'matrix-energia-negociador-divida-v2',
              recoveredValueFrom: {
                source: StatsDataSource.MIDDLEWARE,
                path: '[\'deal-info\'].VALOR_RECUPERADO',
              },
              currentDebit: {
                source: StatsDataSource.CUSTOM_DATA,
                path: 'VALOR_CORRIGIDO',
              },
            },
          ],
        },
      };

      const targetWorkflowId = 'matrix-energia-negociador-divida-v1';
      
      // Simulate the lookup logic from getRecoveredValueConfig
      const workflowConfig = customerPreferences.portfolio.statsConfig.find(
        (config: any) => config.workflowId === targetWorkflowId,
      );

      expect(workflowConfig).toBeDefined();
      expect(workflowConfig.workflowId).toBe(targetWorkflowId);
      expect(workflowConfig['recoveredValueFrom']).toBeDefined();
      expect(workflowConfig['recoveredValueFrom'].source).toBe(StatsDataSource.MIDDLEWARE);
      expect(workflowConfig['currentDebit']).toBeDefined();
      expect(workflowConfig['currentDebit'].source).toBe(StatsDataSource.MIDDLEWARE);
    });
  });
});
