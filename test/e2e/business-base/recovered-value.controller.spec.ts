import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { getAuthCredentials } from 'test/helpers/authenticated-user';
import { CommunicationChannel, MessageType } from '@common/enums';

describe('Recovered Value (e2e)', () => {
  let app: INestApplication;
  let testWorkflowId: string;
  let testCustomerId: string;
  let testPortfolioId: string;
  let testPortfolioItemId: string;
  let authCredentials: any;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    authCredentials = await getAuthCredentials(app);
    testCustomerId = authCredentials.customerId;

    // Create agent for workflow
    const agent = {
      role: 'recovered-value-test-agent',
      backstory: 'I am an agent that processes recovered value data.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    const agentId = createAgentResponse.body.data.id;

    // Create task
    const task = {
      description: 'Process recovered value data for {{name}} with phone {{phone_number}} and recovered value {{VALOR_RECEBIDO}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    const taskId = createTaskResponse.body.data.id;

    // Create workflow
    const workflow = {
      name: 'recovered-value-test-workflow',
      description: 'Test workflow for recovered value functionality',
      steps: [
        {
          description: 'Process recovered value',
          order: 1,
          taskId: taskId,
          params: {},
          middlewares: [],
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    testWorkflowId = createWorkflowResponse.body.data.id;

    // Create customer preferences with recovered value configuration
    const customerPreferences = {
      portfolio: {
        defaultWorkflowId: testWorkflowId,
        timezoneUTC: '-3',
        recoveredValueFrom: {
          source: 'customData',
          path: 'VALOR_RECEBIDO',
        },
      },
    };

    await request(app.getHttpServer())
      .post(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
      .set('Authorization', `Bearer ${authCredentials.accessToken}`)
      .send(customerPreferences)
      .expect(201);

    // Create portfolio
    const portfolio = {
      id: uuidv4(),
      name: 'Test Recovered Value Portfolio',
      workflowId: testWorkflowId,
      workExpression: '0 9 * * 1-5',
      executeImmediately: false,
      communicationChannel: CommunicationChannel.MOCK,
    };

    const createPortfolioResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolios')
      .set('Authorization', `Bearer ${authCredentials.accessToken}`)
      .send(portfolio)
      .expect(201);

    testPortfolioId = createPortfolioResponse.body.data.id;

    // Create portfolio item with custom data containing recovered value
    const portfolioItem = {
      portfolioId: testPortfolioId,
      phoneNumber: '+5511999999999',
      customData: {
        name: 'Test Customer',
        phone_number: '+5511999999999',
        VALOR_RECEBIDO: '1,500.75',
      },
      line: 1,
    };

    const createPortfolioItemResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio-items')
      .send(portfolioItem)
      .expect(201);

    testPortfolioItemId = createPortfolioItemResponse.body.data.id;

    // Simulate recovered value extraction and storage
    await request(app.getHttpServer())
      .post('/api/v1/business-base/collect-cash-stats')
      .send({
        customerId: testCustomerId,
        portfolioId: testPortfolioId,
        portfolioItemId: testPortfolioItemId,
        workflowId: testWorkflowId,
        valorRecuperado: 1500.75,
        quantidadeDeParcelas: 1,
      })
      .expect(201);
  });

  describe('GET /api/v1/business-base/portfolios/recovered-values', () => {
    it('should return customer recovered values', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('customerId', testCustomerId);
      expect(response.body.data).toHaveProperty('totalRecoveredValue');
      expect(response.body.data).toHaveProperty('portfolios');
      expect(Array.isArray(response.body.data.portfolios)).toBe(true);

      if (response.body.data.portfolios.length > 0) {
        const portfolio = response.body.data.portfolios[0];
        expect(portfolio).toHaveProperty('portfolioId');
        expect(portfolio).toHaveProperty('portfolioName');
        expect(portfolio).toHaveProperty('totalRecoveredValue');
        expect(portfolio).toHaveProperty('itemCount');
      }
    });

    it('should support date filtering', async () => {
      const startDate = new Date('2024-01-01').toISOString();
      const endDate = new Date('2024-12-31').toISOString();

      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .query({ startDate, endDate })
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('customerId', testCustomerId);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .expect(401);
    });
  });

  describe('GET /api/v1/business-base/portfolios/:portfolioId/recovered-values', () => {
    it('should return portfolio recovered values', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${testPortfolioId}/recovered-values`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('portfolioId', testPortfolioId);
      expect(response.body.data).toHaveProperty('portfolioName');
      expect(response.body.data).toHaveProperty('totalRecoveredValue');
      expect(response.body.data).toHaveProperty('itemCount');
    });

    it('should support date filtering', async () => {
      const startDate = new Date('2024-01-01').toISOString();
      const endDate = new Date('2024-12-31').toISOString();

      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${testPortfolioId}/recovered-values`)
        .query({ startDate, endDate })
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('portfolioId', testPortfolioId);
    });

    it('should return 404 for non-existent portfolio', async () => {
      const nonExistentPortfolioId = uuidv4();

      await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${nonExistentPortfolioId}/recovered-values`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(404);
    });
  });

  describe('GET /api/v1/business-base/portfolios (with includeRecoveredValues)', () => {
    it('should include recovered values when requested', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios')
        .query({ includeRecoveredValues: 'true' })
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);

      if (response.body.data.length > 0) {
        const portfolio = response.body.data.find((p: any) => p.id === testPortfolioId);
        if (portfolio) {
          expect(portfolio).toHaveProperty('recoveredValue');
          expect(portfolio.recoveredValue).toHaveProperty('totalRecoveredValue');
          expect(portfolio.recoveredValue).toHaveProperty('itemCount');
        }
      }
    });

    it('should not include recovered values by default', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);

      if (response.body.data.length > 0) {
        const portfolio = response.body.data[0];
        expect(portfolio).not.toHaveProperty('recoveredValue');
      }
    });
  });

  describe('GET /api/v1/business-base/portfolios/:portfolioId (with includeRecoveredValues)', () => {
    it('should include recovered values when requested', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${testPortfolioId}`)
        .query({ includeRecoveredValues: 'true' })
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('recoveredValue');
      expect(response.body.data.recoveredValue).toHaveProperty('totalRecoveredValue');
      expect(response.body.data.recoveredValue).toHaveProperty('itemCount');
    });

    it('should not include recovered values by default', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${testPortfolioId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).not.toHaveProperty('recoveredValue');
    });
  });

  describe('Customer Preference Configurations', () => {
    it('should handle customData source configuration', async () => {
      // Update customer preferences to use customData source
      const customerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'customData',
            path: 'VALOR_RECEBIDO',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferences)
        .expect(200);

      // Test that the configuration is applied
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.data.portfolio.recoveredValueFrom.source).toBe('customData');
      expect(response.body.data.portfolio.recoveredValueFrom.path).toBe('VALOR_RECEBIDO');
    });

    it('should handle middlewareResponse source configuration', async () => {
      // Update customer preferences to use middlewareResponse source
      const customerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'middlewareResponse',
            path: 'acordo-info.valorTotalAcordo',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferences)
        .expect(200);

      // Test that the configuration is applied
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.data.portfolio.recoveredValueFrom.source).toBe('middlewareResponse');
      expect(response.body.data.portfolio.recoveredValueFrom.path).toBe('acordo-info.valorTotalAcordo');
    });

    it('should handle nested path configurations', async () => {
      // Test nested path configuration
      const customerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'customData',
            path: 'payment.details.recoveredAmount',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferences)
        .expect(200);

      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.data.portfolio.recoveredValueFrom.path).toBe('payment.details.recoveredAmount');
    });

    it('should handle missing recoveredValueFrom configuration', async () => {
      // Update customer preferences without recoveredValueFrom
      const customerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferences)
        .expect(200);

      // Should still return valid response with zero values
      const response = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('customerId', testCustomerId);
      expect(response.body.data).toHaveProperty('totalRecoveredValue');
    });

    it('should validate recoveredValueFrom source enum', async () => {
      // Test invalid source value
      const invalidCustomerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'invalidSource',
            path: 'VALOR_RECEBIDO',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(invalidCustomerPreferences)
        .expect(400);
    });

    it('should validate recoveredValueFrom path is required', async () => {
      // Test missing path
      const invalidCustomerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'customData',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(invalidCustomerPreferences)
        .expect(400);
    });
  });
});
