import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { getAuthCredentials } from 'test/helpers/authenticated-user';
import { CommunicationChannel, MessageType } from '@common/enums';

describe('Recovered Value Automatic Storage (e2e)', () => {
  let app: INestApplication;
  let testWorkflowId: string;
  let testCustomerId: string;
  let testPortfolioId: string;
  let testPortfolioItemId: string;
  let authCredentials: any;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    authCredentials = await getAuthCredentials(app, '<EMAIL>', 'password123');
    testCustomerId = authCredentials.payload.customerId;

    // Create agent for workflow
    const agent = {
      role: 'automatic-recovery-test-agent',
      backstory: 'I am an agent that processes automatic recovered value storage.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    const agentId = createAgentResponse.body.data.id;

    // Create task
    const task = {
      description: 'Process automatic recovered value storage for {{name}} with phone {{phone_number}} and recovered value {{VALOR_RECEBIDO}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    const taskId = createTaskResponse.body.data.id;

    // Create workflow
    const workflow = {
      name: 'automatic-recovery-test-workflow',
      description: 'Test workflow for automatic recovered value storage',
      steps: [
        {
          description: 'Process automatic recovered value',
          order: 1,
          taskId: taskId,
          params: {},
          middlewares: [],
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    testWorkflowId = createWorkflowResponse.body.data.id;

    // Create customer preferences with recovered value configuration
    const customerPreferences = {
      portfolio: {
        defaultWorkflowId: testWorkflowId,
        timezoneUTC: '-3',
        recoveredValueFrom: {
          source: 'customData',
          path: 'VALOR_RECEBIDO',
        },
      },
    };

    await request(app.getHttpServer())
      .post(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
      .set('Authorization', `Bearer ${authCredentials.accessToken}`)
      .send(customerPreferences)
      .expect(201);

    // Create portfolio
    const portfolio = {
      id: uuidv4(),
      name: 'Test Automatic Recovery Portfolio',
      workflowId: testWorkflowId,
      workExpression: '0 9 * * 1-5',
      executeImmediately: false,
      communicationChannel: CommunicationChannel.MOCK,
    };

    const createPortfolioResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolios')
      .set('Authorization', `Bearer ${authCredentials.accessToken}`)
      .send(portfolio)
      .expect(201);

    testPortfolioId = createPortfolioResponse.body.data.id;

    // Create portfolio item with custom data containing recovered value
    const portfolioItem = {
      portfolioId: testPortfolioId,
      phoneNumber: '+5511888888888',
      customData: {
        name: 'Test Automatic Customer',
        phone_number: '+5511888888888',
        VALOR_RECEBIDO: '2,750.25',
      },
      line: 1,
    };

    const createPortfolioItemResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio-items')
      .send(portfolioItem)
      .expect(201);

    testPortfolioItemId = createPortfolioItemResponse.body.data.id;
  });

  describe('Automatic Recovered Value Storage on SUCCEED Status', () => {
    it('should automatically store recovered value when portfolio item status changes to SUCCEED via API', async () => {
      // Get initial recovered values count
      const initialResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const initialTotalValue = initialResponse.body.data.totalRecoveredValue;

      // Update portfolio item status to SUCCEED via API endpoint
      await request(app.getHttpServer())
        .put(`/api/v1/business-base/portfolio-items/${testPortfolioItemId}/succeed`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      // Wait a moment for async processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check that recovered value was automatically stored
      const finalResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const finalTotalValue = finalResponse.body.data.totalRecoveredValue;

      // Verify that the recovered value was added
      expect(finalTotalValue).toBeGreaterThan(initialTotalValue);
      expect(finalTotalValue - initialTotalValue).toBeCloseTo(2750.25, 2);

      // Verify portfolio-specific recovered values
      const portfolioResponse = await request(app.getHttpServer())
        .get(`/api/v1/business-base/portfolios/${testPortfolioId}/recovered-values`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      expect(portfolioResponse.body.data.totalRecoveredValue).toBeCloseTo(2750.25, 2);
      expect(portfolioResponse.body.data.itemCount).toBeGreaterThan(0);
    });

    it('should handle missing customer preferences gracefully', async () => {
      // Create a new portfolio item for a customer without recovered value preferences
      const portfolioItemWithoutPrefs = {
        portfolioId: testPortfolioId,
        phoneNumber: '+5511777777777',
        customData: {
          name: 'Test Customer No Prefs',
          phone_number: '+5511777777777',
          VALOR_RECEBIDO: '1,000.00',
        },
        line: 2,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/business-base/portfolio-items')
        .send(portfolioItemWithoutPrefs)
        .expect(201);

      const portfolioItemId = createResponse.body.data.id;

      // Remove recovered value configuration from customer preferences
      const customerPreferencesWithoutRecovery = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          // No recoveredValueFrom configuration
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferencesWithoutRecovery)
        .expect(200);

      // Get initial recovered values count
      const initialResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const initialTotalValue = initialResponse.body.data.totalRecoveredValue;

      // Update portfolio item status to SUCCEED - should not fail even without preferences
      await request(app.getHttpServer())
        .put(`/api/v1/business-base/portfolio-items/${portfolioItemId}/succeed`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      // Wait a moment for async processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check that recovered value was not added (no configuration)
      const finalResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const finalTotalValue = finalResponse.body.data.totalRecoveredValue;

      // Verify that no additional recovered value was added
      expect(finalTotalValue).toBe(initialTotalValue);
    });

    it('should handle zero recovered values correctly', async () => {
      // Restore customer preferences with recovered value configuration
      const customerPreferences = {
        portfolio: {
          defaultWorkflowId: testWorkflowId,
          timezoneUTC: '-3',
          recoveredValueFrom: {
            source: 'customData',
            path: 'VALOR_RECEBIDO',
          },
        },
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${testCustomerId}`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .send(customerPreferences)
        .expect(200);

      // Create portfolio item with zero recovered value
      const portfolioItemZero = {
        portfolioId: testPortfolioId,
        phoneNumber: '+5511666666666',
        customData: {
          name: 'Test Customer Zero Value',
          phone_number: '+5511666666666',
          VALOR_RECEBIDO: '0.00',
        },
        line: 3,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/api/v1/business-base/portfolio-items')
        .send(portfolioItemZero)
        .expect(201);

      const portfolioItemId = createResponse.body.data.id;

      // Get initial recovered values count
      const initialResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const initialTotalValue = initialResponse.body.data.totalRecoveredValue;

      // Update portfolio item status to SUCCEED
      await request(app.getHttpServer())
        .put(`/api/v1/business-base/portfolio-items/${portfolioItemId}/succeed`)
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      // Wait a moment for async processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check that no recovered value was added (zero value should be skipped)
      const finalResponse = await request(app.getHttpServer())
        .get('/api/v1/business-base/portfolios/recovered-values')
        .set('Authorization', `Bearer ${authCredentials.accessToken}`)
        .expect(200);

      const finalTotalValue = finalResponse.body.data.totalRecoveredValue;

      // Verify that no additional recovered value was added for zero values
      expect(finalTotalValue).toBe(initialTotalValue);
    });
  });
});
