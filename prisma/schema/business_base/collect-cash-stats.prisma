model collectCashStats {
  id                   String   @id @default(uuid()) @db.Uuid
  customerId           String   @map(name: "customer_id") @db.Uuid
  portfolioId          String   @map(name: "portfolio_id") @db.Uuid
  portfolioItemId      String   @map(name: "portfolio_item_id") @db.Uuid
  workflowId           String   @map(name: "workflow_id") @db.Uuid
  dealValue            Decimal  @map(name: "deal_value") @db.Decimal(15, 2)
  installments         Int      @map(name: "installments")
  currentDebt         Decimal  @map(name: "current_debt") @db.Decimal(15, 2)
  originalDebt        Decimal  @map(name: "original_debt") @db.Decimal(15, 2)
  status               String   @default("ACTIVE")
  createdAt            DateTime @default(now()) @map(name: "created_at")
  updatedAt            DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@index([customerId])
  @@index([portfolioId])
  @@index([portfolioItemId])
  @@index([workflowId])
  @@index([customerId, portfolioId])
  @@index([createdAt])
  @@map(name: "collect_cash_stats")
  @@schema("business_base")
}
