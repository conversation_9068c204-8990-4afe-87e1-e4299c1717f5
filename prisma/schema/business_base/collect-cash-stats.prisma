model collectCashStats {
  id                   String   @id @default(uuid()) @db.Uuid
  customerId           String   @map(name: "customer_id") @db.Uuid
  portfolioId          String   @map(name: "portfolio_id") @db.Uuid
  portfolioItemId      String   @map(name: "portfolio_item_id") @db.Uuid
  workflowId           String   @map(name: "workflow_id") @db.Uuid
  valorRecuperado      Decimal  @map(name: "valor_recuperado") @db.Decimal(15, 2)
  quantidadeDeParcelas Int      @map(name: "quantidade_de_parcelas")
  status               String   @default("ACTIVE")
  createdAt            DateTime @default(now()) @map(name: "created_at")
  updatedAt            DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@index([customerId])
  @@index([portfolioId])
  @@index([portfolioItemId])
  @@index([workflowId])
  @@index([customerId, portfolioId])
  @@index([createdAt])
  @@map(name: "collect_cash_stats")
  @@schema("business_base")
}
