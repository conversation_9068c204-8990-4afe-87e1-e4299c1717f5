-- AlterTable
ALTER TABLE "business_base"."portfolio" ALTER COLUMN "follow_up_after" SET DEFAULT 175,
ALTER COLUMN "follow_up_expression" SET DEFAULT '0 11-23/3 * * *',
ALTER COLUMN "max_follow_ups" SET DEFAULT 1;

-- CreateTable
CREATE TABLE "business_base"."collect_cash_stats" (
    "id" UUID NOT NULL,
    "customer_id" UUID NOT NULL,
    "portfolio_id" UUID NOT NULL,
    "portfolio_item_id" UUID NOT NULL,
    "workflow_id" UUID NOT NULL,
    "deal_value" DECIMAL(15,2) NOT NULL,
    "installments" INTEGER NOT NULL,
    "current_debt" DECIMAL(15,2) NOT NULL,
    "original_debt" DECIMAL(15,2) NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "collect_cash_stats_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "collect_cash_stats_id_idx" ON "business_base"."collect_cash_stats"("id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_customer_id_idx" ON "business_base"."collect_cash_stats"("customer_id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_portfolio_id_idx" ON "business_base"."collect_cash_stats"("portfolio_id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_portfolio_item_id_idx" ON "business_base"."collect_cash_stats"("portfolio_item_id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_workflow_id_idx" ON "business_base"."collect_cash_stats"("workflow_id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_customer_id_portfolio_id_idx" ON "business_base"."collect_cash_stats"("customer_id", "portfolio_id");

-- CreateIndex
CREATE INDEX "collect_cash_stats_created_at_idx" ON "business_base"."collect_cash_stats"("created_at");
