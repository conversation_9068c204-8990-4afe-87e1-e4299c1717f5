diff --git a/package-lock.json b/package-lock.json
index 0b833b48..89ffc3e8 100644
--- a/package-lock.json
+++ b/package-lock.json
@@ -399,23 +399,10 @@
         "node": ">=16.0.0"
       }
     },
-    "node_modules/@aws-sdk/client-cloudwatch-logs/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@aws-sdk/client-cognito-identity": {
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/client-cognito-identity/-/client-cognito-identity-3.828.0.tgz",
       "integrity": "sha512-If4vBVbOxFfTW6JP92lWQGhis5fIt4PIS071DoSBf931ss/mISDnslpGGz1rXp6vyS8l7fODb3LDMAzSPyooNg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-crypto/sha256-browser": "5.2.0",
         "@aws-crypto/sha256-js": "5.2.0",
@@ -465,7 +452,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.828.0.tgz",
       "integrity": "sha512-qxw8JcPTaFaBwTBUr4YmLajaMh3En65SuBWAKEtjctbITRRekzR7tvr/TkwoyVOh+XoAtkwOn+BQeQbX+/wgHw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-crypto/sha256-browser": "5.2.0",
         "@aws-crypto/sha256-js": "5.2.0",
@@ -514,7 +500,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/core/-/core-3.826.0.tgz",
       "integrity": "sha512-BGbQYzWj3ps+dblq33FY5tz/SsgJCcXX0zjQlSC07tYvU1jHTUvsefphyig+fY38xZ4wdKjbTop+KUmXUYrOXw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@aws-sdk/xml-builder": "3.821.0",
@@ -540,7 +525,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.826.0.tgz",
       "integrity": "sha512-DK3pQY8+iKK3MGDdC3uOZQ2psU01obaKlTYhEwNu4VWzgwQL4Vi3sWj4xSWGEK41vqZxiRLq6fOq7ysRI+qEZA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -556,7 +540,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.826.0.tgz",
       "integrity": "sha512-N+IVZBh+yx/9GbMZTKO/gErBi/FYZQtcFRItoLbY+6WU+0cSWyZYfkoeOxHmQV3iX9k65oljERIWUmL9x6OSQg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -577,7 +560,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.828.0.tgz",
       "integrity": "sha512-T3DJMo2/j7gCPpFg2+xEHWgua05t8WP89ye7PaZxA2Fc6CgScHkZsJZTri1QQIU2h+eOZ75EZWkeFLIPgN0kRQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/credential-provider-env": "3.826.0",
@@ -601,7 +583,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.828.0.tgz",
       "integrity": "sha512-9z3iPwVYOQYNzVZj8qycZaS/BOSKRXWA+QVNQlfEnQ4sA4sOcKR4kmV2h+rJcuBsSFfmOF62ZDxyIBGvvM4t/w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/credential-provider-env": "3.826.0",
         "@aws-sdk/credential-provider-http": "3.826.0",
@@ -624,7 +605,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.826.0.tgz",
       "integrity": "sha512-kURrc4amu3NLtw1yZw7EoLNEVhmOMRUTs+chaNcmS+ERm3yK0nKjaJzmKahmwlTQTSl3wJ8jjK7x962VPo+zWw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -641,7 +621,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.828.0.tgz",
       "integrity": "sha512-9CEAXzUDSzOjOCb3XfM15TZhTaM+l07kumZyx2z8NC6T2U4qbCJqn4h8mFlRvYrs6cBj2SN40sD3r5Wp0Cq2Kw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/client-sso": "3.828.0",
         "@aws-sdk/core": "3.826.0",
@@ -660,7 +639,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.828.0.tgz",
       "integrity": "sha512-MguDhGHlQBeK9CQ/P4NOY0whAJ4HJU4x+f1dphg3I1sGlccFqfB8Moor2vXNKu0Th2kvAwkn9pr7gGb/+NGR9g==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/nested-clients": "3.828.0",
@@ -677,7 +655,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.821.0.tgz",
       "integrity": "sha512-xSMR+sopSeWGx5/4pAGhhfMvGBHioVBbqGvDs6pG64xfNwM5vq5s5v6D04e2i+uSTj4qGa71dLUs5I0UzAK3sw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -692,7 +669,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.821.0.tgz",
       "integrity": "sha512-0cvI0ipf2tGx7fXYEEN5fBeZDz2RnHyb9xftSgUsEq7NBxjV0yTZfLJw6Za5rjE6snC80dRN8+bTNR1tuG89zA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -706,7 +682,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.821.0.tgz",
       "integrity": "sha512-efmaifbhBoqKG3bAoEfDdcM8hn1psF+4qa7ykWuYmfmah59JBeqHLfz5W9m9JoTwoKPkFcVLWZxnyZzAnVBOIg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -721,7 +696,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.828.0.tgz",
       "integrity": "sha512-nixvI/SETXRdmrVab4D9LvXT3lrXkwAWGWk2GVvQvzlqN1/M/RfClj+o37Sn4FqRkGH9o9g7Fqb1YqZ4mqDAtA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -739,7 +713,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.821.0.tgz",
       "integrity": "sha512-t8og+lRCIIy5nlId0bScNpCkif8sc0LhmtaKsbm0ZPm3sCa/WhCbSZibjbZ28FNjVCV+p0D9RYZx0VDDbtWyjw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/node-config-provider": "^4.1.3",
@@ -756,7 +729,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.828.0.tgz",
       "integrity": "sha512-JdOjI/TxkfQpY/bWbdGMdCiePESXTbtl6MfnJxz35zZ3tfHvBnxAWCoYJirdmjzY/j/dFo5oEyS6mQuXAG9w2w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/nested-clients": "3.828.0",
@@ -774,7 +746,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.821.0.tgz",
       "integrity": "sha512-Znroqdai1a90TlxGaJ+FK1lwC0fHpo97Xjsp5UKGR5JODYm7f9+/fF17ebO1KdoBr/Rm0UIFiF5VmI8ts9F1eA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -787,7 +758,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.828.0.tgz",
       "integrity": "sha512-RvKch111SblqdkPzg3oCIdlGxlQs+k+P7Etory9FmxPHyPDvsP1j1c74PmgYqtzzMWmoXTjd+c9naUHh9xG8xg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -802,7 +772,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.821.0.tgz",
       "integrity": "sha512-irWZHyM0Jr1xhC+38OuZ7JB6OXMLPZlj48thElpsO1ZSLRkLZx5+I7VV6k3sp2yZ7BYbKz/G2ojSv4wdm7XTLw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -814,7 +783,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.828.0.tgz",
       "integrity": "sha512-LdN6fTBzTlQmc8O8f1wiZN0qF3yBWVGis7NwpWK7FUEzP9bEZRxYfIkV9oV9zpt6iNRze1SedK3JQVB/udxBoA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/middleware-user-agent": "3.828.0",
         "@aws-sdk/types": "3.821.0",
@@ -838,7 +806,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.821.0.tgz",
       "integrity": "sha512-DIIotRnefVL6DiaHtO6/21DhJ4JZnnIwdNbpwiAhdt/AVbttcE4yw925gsjur0OGv5BTYXQXU3YnANBYnZjuQA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -851,7 +818,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.0.4.tgz",
       "integrity": "sha512-gJnEjZMvigPDQWHrW3oPrFhQtkrgqBkyjj3pCIdF3A5M6vsZODG93KNlfJprv6bp4245bdT32fsHK4kkH3KYDA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -864,7 +830,6 @@
       "version": "4.1.4",
       "resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.1.4.tgz",
       "integrity": "sha512-prmU+rDddxHOH0oNcwemL+SwnzcG65sBF2yXRO7aeXIn/xTlq2pX7JLVbkBnVLowHLg4/OL4+jBmv9hVrVGS+w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -877,10 +842,9 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/core": {
-      "version": "3.5.3",
-      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.5.3.tgz",
-      "integrity": "sha512-xa5byV9fEguZNofCclv6v9ra0FYh5FATQW/da7FQUVTic94DfrN/NvmKZjrMyzbpqfot9ZjBaO8U1UeTbmSLuA==",
-      "license": "Apache-2.0",
+      "version": "3.7.1",
+      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.7.1.tgz",
+      "integrity": "sha512-ExRCsHnXFtBPnM7MkfKBPcBBdHw1h/QS/cbNw4ho95qnyNHvnpmGbR39MIAv9KggTr5qSPxRSEL+hRXlyGyGQw==",
       "dependencies": {
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/protocol-http": "^5.1.2",
@@ -888,7 +852,7 @@
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-body-length-browser": "^4.0.0",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "@smithy/util-utf8": "^4.0.0",
         "tslib": "^2.6.2"
       },
@@ -900,7 +864,6 @@
       "version": "4.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.6.tgz",
       "integrity": "sha512-hKMWcANhUiNbCJouYkZ9V3+/Qf9pteR1dnwgdyzR09R4ODEYx8BbUysHwRSyex4rZ9zapddZhLFTnT4ZijR4pw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
@@ -913,10 +876,9 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/fetch-http-handler": {
-      "version": "5.0.4",
-      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.0.4.tgz",
-      "integrity": "sha512-AMtBR5pHppYMVD7z7G+OlHHAcgAN7v0kVKEpHuTO4Gb199Gowh0taYi9oDStFeUhetkeP55JLSVlTW1n9rFtUw==",
-      "license": "Apache-2.0",
+      "version": "5.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.1.0.tgz",
+      "integrity": "sha512-mADw7MS0bYe2OGKkHYMaqarOXuDwRbO6ArD91XhHcl2ynjGCFF+hvqf0LyQcYxkA1zaWjefSkU7Ne9mqgApSgQ==",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/querystring-builder": "^4.0.4",
@@ -932,7 +894,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.0.4.tgz",
       "integrity": "sha512-qnbTPUhCVnCgBp4z4BUJUhOEkVwxiEi1cyFM+Zj6o+aY8OFGxUQleKWq8ltgp3dujuhXojIvJWdoqpm6dVO3lQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -947,7 +908,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.0.4.tgz",
       "integrity": "sha512-bNYMi7WKTJHu0gn26wg8OscncTt1t2b8KcsZxvOv56XA6cyXtOAAAaNP7+m45xfppXfOatXF3Sb1MNsLUgVLTw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -960,7 +920,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz",
       "integrity": "sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -972,7 +931,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.0.4.tgz",
       "integrity": "sha512-F7gDyfI2BB1Kc+4M6rpuOLne5LOcEknH1n6UQB69qv+HucXBR1rkzXBnQTB2q46sFy1PM/zuSJOB532yc8bg3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -983,12 +941,11 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/middleware-endpoint": {
-      "version": "4.1.11",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.11.tgz",
-      "integrity": "sha512-zDogwtRLzKl58lVS8wPcARevFZNBOOqnmzWWxVe9XiaXU2CADFjvJ9XfNibgkOWs08sxLuSr81NrpY4mgp9OwQ==",
-      "license": "Apache-2.0",
+      "version": "4.1.16",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.16.tgz",
+      "integrity": "sha512-plpa50PIGLqzMR2ANKAw2yOW5YKS626KYKqae3atwucbz4Ve4uQ9K9BEZxDLIFmCu7hKLcrq2zmj4a+PfmUV5w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
+        "@smithy/core": "^3.7.1",
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -1002,18 +959,17 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/middleware-retry": {
-      "version": "4.1.12",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.12.tgz",
-      "integrity": "sha512-wvIH70c4e91NtRxdaLZF+mbLZ/HcC6yg7ySKUiufL6ESp6zJUSnJucZ309AvG9nqCFHSRB5I6T3Ez1Q9wCh0Ww==",
-      "license": "Apache-2.0",
+      "version": "4.1.17",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.17.tgz",
+      "integrity": "sha512-gsCimeG6BApj0SBecwa1Be+Z+JOJe46iy3B3m3A8jKJHf7eIihP76Is4LwLrbJ1ygoS7Vg73lfqzejmLOrazUA==",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/protocol-http": "^5.1.2",
-        "@smithy/service-error-classification": "^4.0.5",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/service-error-classification": "^4.0.6",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-retry": "^4.0.5",
+        "@smithy/util-retry": "^4.0.6",
         "tslib": "^2.6.2",
         "uuid": "^9.0.1"
       },
@@ -1025,7 +981,6 @@
       "version": "4.0.8",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.0.8.tgz",
       "integrity": "sha512-iSSl7HJoJaGyMIoNn2B7czghOVwJ9nD7TMvLhMWeSB5vt0TnEYyRRqPJu/TqW76WScaNvYYB8nRoiBHR9S1Ddw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -1039,7 +994,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.0.4.tgz",
       "integrity": "sha512-kagK5ggDrBUCCzI93ft6DjteNSfY8Ulr83UtySog/h09lTIOAJ/xUSObutanlPT0nhoHAkpmW9V5K8oPyLh+QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1052,7 +1006,6 @@
       "version": "4.1.3",
       "resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.1.3.tgz",
       "integrity": "sha512-HGHQr2s59qaU1lrVH6MbLlmOBxadtzTsoO4c+bF5asdgVik3I8o7JIOzoeqWc5MjVa+vD36/LWE0iXKpNqooRw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -1064,10 +1017,9 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/node-http-handler": {
-      "version": "4.0.6",
-      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.0.6.tgz",
-      "integrity": "sha512-NqbmSz7AW2rvw4kXhKGrYTiJVDHnMsFnX4i+/FzcZAfbOBauPYs2ekuECkSbtqaxETLLTu9Rl/ex6+I2BKErPA==",
-      "license": "Apache-2.0",
+      "version": "4.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.1.0.tgz",
+      "integrity": "sha512-vqfSiHz2v8b3TTTrdXi03vNz1KLYYS3bhHCDv36FYDqxT7jvTll1mMnCrkD+gOvgwybuunh/2VmvOMqwBegxEg==",
       "dependencies": {
         "@smithy/abort-controller": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
@@ -1083,7 +1035,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.4.tgz",
       "integrity": "sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1096,7 +1047,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.1.2.tgz",
       "integrity": "sha512-rOG5cNLBXovxIrICSBm95dLqzfvxjEmuZx4KK3hWwPFHGdW3lxY0fZNXfv2zebfRO7sJZ5pKJYHScsqopeIWtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1109,7 +1059,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.0.4.tgz",
       "integrity": "sha512-SwREZcDnEYoh9tLNgMbpop+UTGq44Hl9tdj3rf+yeLcfH7+J8OXEBaMc2kDxtyRHu8BhSg9ADEx0gFHvpJgU8w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-uri-escape": "^4.0.0",
@@ -1123,7 +1072,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.0.4.tgz",
       "integrity": "sha512-6yZf53i/qB8gRHH/l2ZwUG5xgkPgQF15/KxH0DdXMDHjesA9MeZje/853ifkSY0x4m5S+dfDZ+c4x439PF0M2w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1133,10 +1081,9 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/service-error-classification": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.5.tgz",
-      "integrity": "sha512-LvcfhrnCBvCmTee81pRlh1F39yTS/+kYleVeLCwNtkY8wtGg8V/ca9rbZZvYIl8OjlMtL6KIjaiL/lgVqHD2nA==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.6.tgz",
+      "integrity": "sha512-RRoTDL//7xi4tn5FrN2NzH17jbgmnKidUqd4KvquT0954/i6CXXkh1884jBiunq24g9cGtPBEXlU40W6EpNOOg==",
       "dependencies": {
         "@smithy/types": "^4.3.1"
       },
@@ -1148,7 +1095,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.4.tgz",
       "integrity": "sha512-63X0260LoFBjrHifPDs+nM9tV0VMkOTl4JRMYNuKh/f5PauSjowTfvF3LogfkWdcPoxsA9UjqEOgjeYIbhb7Nw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1161,7 +1107,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.1.2.tgz",
       "integrity": "sha512-d3+U/VpX7a60seHziWnVZOHuEgJlclufjkS6zhXvxcJgkJq4UWdH5eOBLzHRMx6gXjsdT9h6lfpmLzbrdupHgQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -1177,17 +1122,16 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/smithy-client": {
-      "version": "4.4.3",
-      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.3.tgz",
-      "integrity": "sha512-xxzNYgA0HD6ETCe5QJubsxP0hQH3QK3kbpJz3QrosBCuIWyEXLR/CO5hFb2OeawEKUxMNhz3a1nuJNN2np2RMA==",
-      "license": "Apache-2.0",
+      "version": "4.4.8",
+      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.8.tgz",
+      "integrity": "sha512-pcW691/lx7V54gE+dDGC26nxz8nrvnvRSCJaIYD6XLPpOInEZeKdV/SpSux+wqeQ4Ine7LJQu8uxMvobTIBK0w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
-        "@smithy/middleware-endpoint": "^4.1.11",
+        "@smithy/core": "^3.7.1",
+        "@smithy/middleware-endpoint": "^4.1.16",
         "@smithy/middleware-stack": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "tslib": "^2.6.2"
       },
       "engines": {
@@ -1198,7 +1142,6 @@
       "version": "4.3.1",
       "resolved": "https://registry.npmjs.org/@smithy/types/-/types-4.3.1.tgz",
       "integrity": "sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1210,7 +1153,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.0.4.tgz",
       "integrity": "sha512-eMkc144MuN7B0TDA4U2fKs+BqczVbk3W+qIvcoCY6D1JY3hnAdCuhCZODC+GAeaxj0p6Jroz4+XMUn3PCxQQeQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/querystring-parser": "^4.0.4",
         "@smithy/types": "^4.3.1",
@@ -1224,7 +1166,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.0.0.tgz",
       "integrity": "sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "@smithy/util-utf8": "^4.0.0",
@@ -1238,7 +1179,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz",
       "integrity": "sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1250,7 +1190,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz",
       "integrity": "sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1262,7 +1201,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz",
       "integrity": "sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "tslib": "^2.6.2"
@@ -1275,7 +1213,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz",
       "integrity": "sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1284,13 +1221,12 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/util-defaults-mode-browser": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.19.tgz",
-      "integrity": "sha512-mvLMh87xSmQrV5XqnUYEPoiFFeEGYeAKIDDKdhE2ahqitm8OHM3aSvhqL6rrK6wm1brIk90JhxDf5lf2hbrLbQ==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.24.tgz",
+      "integrity": "sha512-UkQNgaQ+bidw1MgdgPO1z1k95W/v8Ej/5o/T/Is8PiVUYPspl/ZxV6WO/8DrzZQu5ULnmpB9CDdMSRwgRc21AA==",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "bowser": "^2.11.0",
         "tslib": "^2.6.2"
@@ -1300,16 +1236,15 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/util-defaults-mode-node": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.19.tgz",
-      "integrity": "sha512-8tYnx+LUfj6m+zkUUIrIQJxPM1xVxfRBvoGHua7R/i6qAxOMjqR6CpEpDwKoIs1o0+hOjGvkKE23CafKL0vJ9w==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.24.tgz",
+      "integrity": "sha512-phvGi/15Z4MpuQibTLOYIumvLdXb+XIJu8TA55voGgboln85jytA3wiD7CkUE8SNcWqkkb+uptZKPiuFouX/7g==",
       "dependencies": {
         "@smithy/config-resolver": "^4.1.4",
         "@smithy/credential-provider-imds": "^4.0.6",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -1321,7 +1256,6 @@
       "version": "3.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.0.6.tgz",
       "integrity": "sha512-YARl3tFL3WgPuLzljRUnrS2ngLiUtkwhQtj8PAL13XZSyUiNLQxwG3fBBq3QXFqGFUXepIN73pINp3y8c2nBmA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -1335,7 +1269,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz",
       "integrity": "sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1347,7 +1280,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.0.4.tgz",
       "integrity": "sha512-9MLKmkBmf4PRb0ONJikCbCwORACcil6gUWojwARCClT7RmLzF04hUR4WdRprIXal7XVyrddadYNfp2eF3nrvtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -1357,12 +1289,11 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/util-retry": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.5.tgz",
-      "integrity": "sha512-V7MSjVDTlEt/plmOFBn1762Dyu5uqMrV2Pl2X0dYk4XvWfdWJNe9Bs5Bzb56wkCuiWjSfClVMGcsuKrGj7S/yg==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.6.tgz",
+      "integrity": "sha512-+YekoF2CaSMv6zKrA6iI/N9yva3Gzn4L6n35Luydweu5MMPYpiGZlWqehPHDHyNbnyaYlz/WJyYAZnC+loBDZg==",
       "dependencies": {
-        "@smithy/service-error-classification": "^4.0.5",
+        "@smithy/service-error-classification": "^4.0.6",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -1371,13 +1302,12 @@
       }
     },
     "node_modules/@aws-sdk/client-cognito-identity/node_modules/@smithy/util-stream": {
-      "version": "4.2.2",
-      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.2.tgz",
-      "integrity": "sha512-aI+GLi7MJoVxg24/3J1ipwLoYzgkB4kUfogZfnslcYlynj3xsQ0e7vk4TnTro9hhsS5PvX1mwmkRqqHQjwcU7w==",
-      "license": "Apache-2.0",
+      "version": "4.2.3",
+      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.3.tgz",
+      "integrity": "sha512-cQn412DWHHFNKrQfbHY8vSFI3nTROY1aIKji9N0tpp8gUABRilr7wdf8fqBbSlXresobM+tQFNk6I+0LXK/YZg==",
       "dependencies": {
-        "@smithy/fetch-http-handler": "^5.0.4",
-        "@smithy/node-http-handler": "^4.0.6",
+        "@smithy/fetch-http-handler": "^5.1.0",
+        "@smithy/node-http-handler": "^4.1.0",
         "@smithy/types": "^4.3.1",
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -1393,7 +1323,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz",
       "integrity": "sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -1405,7 +1334,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.0.0.tgz",
       "integrity": "sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "tslib": "^2.6.2"
@@ -1414,19 +1342,6 @@
         "node": ">=18.0.0"
       }
     },
-    "node_modules/@aws-sdk/client-cognito-identity/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "license": "MIT",
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@aws-sdk/client-dynamodb": {
       "version": "3.693.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/client-dynamodb/-/client-dynamodb-3.693.0.tgz",
@@ -1506,18 +1421,6 @@
         "node": ">=16.0.0"
       }
     },
-    "node_modules/@aws-sdk/client-dynamodb/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@aws-sdk/client-s3": {
       "version": "3.693.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/client-s3/-/client-s3-3.693.0.tgz",
@@ -1976,7 +1879,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-cognito-identity/-/credential-provider-cognito-identity-3.828.0.tgz",
       "integrity": "sha512-JmQivvkif6KLVeIKCSybl80aZgbVXREAAa0VwhJg3z2E3r8dm9qnH3XeDAzzoZrJAQmK3czuowWpwlI/PHVX8w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/client-cognito-identity": "3.828.0",
         "@aws-sdk/types": "3.821.0",
@@ -1992,7 +1894,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.821.0.tgz",
       "integrity": "sha512-Znroqdai1a90TlxGaJ+FK1lwC0fHpo97Xjsp5UKGR5JODYm7f9+/fF17ebO1KdoBr/Rm0UIFiF5VmI8ts9F1eA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2005,7 +1906,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.4.tgz",
       "integrity": "sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2018,7 +1918,6 @@
       "version": "4.3.1",
       "resolved": "https://registry.npmjs.org/@smithy/types/-/types-4.3.1.tgz",
       "integrity": "sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -2176,7 +2075,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-providers/-/credential-providers-3.828.0.tgz",
       "integrity": "sha512-PuLp94Brs3lpZ+H8Ata0nolqUEdmV1geiggEzRdLt93cggs7UXPJG9TG87py6W4qPmRvkE34QSwO/1NXpm4eGw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/client-cognito-identity": "3.828.0",
         "@aws-sdk/core": "3.826.0",
@@ -2206,7 +2104,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/client-sso/-/client-sso-3.828.0.tgz",
       "integrity": "sha512-qxw8JcPTaFaBwTBUr4YmLajaMh3En65SuBWAKEtjctbITRRekzR7tvr/TkwoyVOh+XoAtkwOn+BQeQbX+/wgHw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-crypto/sha256-browser": "5.2.0",
         "@aws-crypto/sha256-js": "5.2.0",
@@ -2255,7 +2152,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/core/-/core-3.826.0.tgz",
       "integrity": "sha512-BGbQYzWj3ps+dblq33FY5tz/SsgJCcXX0zjQlSC07tYvU1jHTUvsefphyig+fY38xZ4wdKjbTop+KUmXUYrOXw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@aws-sdk/xml-builder": "3.821.0",
@@ -2281,7 +2177,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-env/-/credential-provider-env-3.826.0.tgz",
       "integrity": "sha512-DK3pQY8+iKK3MGDdC3uOZQ2psU01obaKlTYhEwNu4VWzgwQL4Vi3sWj4xSWGEK41vqZxiRLq6fOq7ysRI+qEZA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -2297,7 +2192,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-http/-/credential-provider-http-3.826.0.tgz",
       "integrity": "sha512-N+IVZBh+yx/9GbMZTKO/gErBi/FYZQtcFRItoLbY+6WU+0cSWyZYfkoeOxHmQV3iX9k65oljERIWUmL9x6OSQg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -2318,7 +2212,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-ini/-/credential-provider-ini-3.828.0.tgz",
       "integrity": "sha512-T3DJMo2/j7gCPpFg2+xEHWgua05t8WP89ye7PaZxA2Fc6CgScHkZsJZTri1QQIU2h+eOZ75EZWkeFLIPgN0kRQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/credential-provider-env": "3.826.0",
@@ -2342,7 +2235,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-node/-/credential-provider-node-3.828.0.tgz",
       "integrity": "sha512-9z3iPwVYOQYNzVZj8qycZaS/BOSKRXWA+QVNQlfEnQ4sA4sOcKR4kmV2h+rJcuBsSFfmOF62ZDxyIBGvvM4t/w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/credential-provider-env": "3.826.0",
         "@aws-sdk/credential-provider-http": "3.826.0",
@@ -2365,7 +2257,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-process/-/credential-provider-process-3.826.0.tgz",
       "integrity": "sha512-kURrc4amu3NLtw1yZw7EoLNEVhmOMRUTs+chaNcmS+ERm3yK0nKjaJzmKahmwlTQTSl3wJ8jjK7x962VPo+zWw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -2382,7 +2273,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-sso/-/credential-provider-sso-3.828.0.tgz",
       "integrity": "sha512-9CEAXzUDSzOjOCb3XfM15TZhTaM+l07kumZyx2z8NC6T2U4qbCJqn4h8mFlRvYrs6cBj2SN40sD3r5Wp0Cq2Kw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/client-sso": "3.828.0",
         "@aws-sdk/core": "3.826.0",
@@ -2401,7 +2291,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/credential-provider-web-identity/-/credential-provider-web-identity-3.828.0.tgz",
       "integrity": "sha512-MguDhGHlQBeK9CQ/P4NOY0whAJ4HJU4x+f1dphg3I1sGlccFqfB8Moor2vXNKu0Th2kvAwkn9pr7gGb/+NGR9g==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/nested-clients": "3.828.0",
@@ -2418,7 +2307,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.821.0.tgz",
       "integrity": "sha512-xSMR+sopSeWGx5/4pAGhhfMvGBHioVBbqGvDs6pG64xfNwM5vq5s5v6D04e2i+uSTj4qGa71dLUs5I0UzAK3sw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -2433,7 +2321,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.821.0.tgz",
       "integrity": "sha512-0cvI0ipf2tGx7fXYEEN5fBeZDz2RnHyb9xftSgUsEq7NBxjV0yTZfLJw6Za5rjE6snC80dRN8+bTNR1tuG89zA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -2447,7 +2334,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.821.0.tgz",
       "integrity": "sha512-efmaifbhBoqKG3bAoEfDdcM8hn1psF+4qa7ykWuYmfmah59JBeqHLfz5W9m9JoTwoKPkFcVLWZxnyZzAnVBOIg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -2462,7 +2348,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.828.0.tgz",
       "integrity": "sha512-nixvI/SETXRdmrVab4D9LvXT3lrXkwAWGWk2GVvQvzlqN1/M/RfClj+o37Sn4FqRkGH9o9g7Fqb1YqZ4mqDAtA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -2480,7 +2365,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.821.0.tgz",
       "integrity": "sha512-t8og+lRCIIy5nlId0bScNpCkif8sc0LhmtaKsbm0ZPm3sCa/WhCbSZibjbZ28FNjVCV+p0D9RYZx0VDDbtWyjw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/node-config-provider": "^4.1.3",
@@ -2497,7 +2381,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/token-providers/-/token-providers-3.828.0.tgz",
       "integrity": "sha512-JdOjI/TxkfQpY/bWbdGMdCiePESXTbtl6MfnJxz35zZ3tfHvBnxAWCoYJirdmjzY/j/dFo5oEyS6mQuXAG9w2w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/nested-clients": "3.828.0",
@@ -2515,7 +2398,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.821.0.tgz",
       "integrity": "sha512-Znroqdai1a90TlxGaJ+FK1lwC0fHpo97Xjsp5UKGR5JODYm7f9+/fF17ebO1KdoBr/Rm0UIFiF5VmI8ts9F1eA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2528,7 +2410,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.828.0.tgz",
       "integrity": "sha512-RvKch111SblqdkPzg3oCIdlGxlQs+k+P7Etory9FmxPHyPDvsP1j1c74PmgYqtzzMWmoXTjd+c9naUHh9xG8xg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -2543,7 +2424,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.821.0.tgz",
       "integrity": "sha512-irWZHyM0Jr1xhC+38OuZ7JB6OXMLPZlj48thElpsO1ZSLRkLZx5+I7VV6k3sp2yZ7BYbKz/G2ojSv4wdm7XTLw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -2555,7 +2435,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.828.0.tgz",
       "integrity": "sha512-LdN6fTBzTlQmc8O8f1wiZN0qF3yBWVGis7NwpWK7FUEzP9bEZRxYfIkV9oV9zpt6iNRze1SedK3JQVB/udxBoA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/middleware-user-agent": "3.828.0",
         "@aws-sdk/types": "3.821.0",
@@ -2579,7 +2458,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.821.0.tgz",
       "integrity": "sha512-DIIotRnefVL6DiaHtO6/21DhJ4JZnnIwdNbpwiAhdt/AVbttcE4yw925gsjur0OGv5BTYXQXU3YnANBYnZjuQA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2592,7 +2470,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.0.4.tgz",
       "integrity": "sha512-gJnEjZMvigPDQWHrW3oPrFhQtkrgqBkyjj3pCIdF3A5M6vsZODG93KNlfJprv6bp4245bdT32fsHK4kkH3KYDA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2605,7 +2482,6 @@
       "version": "4.1.4",
       "resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.1.4.tgz",
       "integrity": "sha512-prmU+rDddxHOH0oNcwemL+SwnzcG65sBF2yXRO7aeXIn/xTlq2pX7JLVbkBnVLowHLg4/OL4+jBmv9hVrVGS+w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -2618,10 +2494,9 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/core": {
-      "version": "3.5.3",
-      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.5.3.tgz",
-      "integrity": "sha512-xa5byV9fEguZNofCclv6v9ra0FYh5FATQW/da7FQUVTic94DfrN/NvmKZjrMyzbpqfot9ZjBaO8U1UeTbmSLuA==",
-      "license": "Apache-2.0",
+      "version": "3.7.1",
+      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.7.1.tgz",
+      "integrity": "sha512-ExRCsHnXFtBPnM7MkfKBPcBBdHw1h/QS/cbNw4ho95qnyNHvnpmGbR39MIAv9KggTr5qSPxRSEL+hRXlyGyGQw==",
       "dependencies": {
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/protocol-http": "^5.1.2",
@@ -2629,7 +2504,7 @@
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-body-length-browser": "^4.0.0",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "@smithy/util-utf8": "^4.0.0",
         "tslib": "^2.6.2"
       },
@@ -2641,7 +2516,6 @@
       "version": "4.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.6.tgz",
       "integrity": "sha512-hKMWcANhUiNbCJouYkZ9V3+/Qf9pteR1dnwgdyzR09R4ODEYx8BbUysHwRSyex4rZ9zapddZhLFTnT4ZijR4pw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
@@ -2654,10 +2528,9 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/fetch-http-handler": {
-      "version": "5.0.4",
-      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.0.4.tgz",
-      "integrity": "sha512-AMtBR5pHppYMVD7z7G+OlHHAcgAN7v0kVKEpHuTO4Gb199Gowh0taYi9oDStFeUhetkeP55JLSVlTW1n9rFtUw==",
-      "license": "Apache-2.0",
+      "version": "5.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.1.0.tgz",
+      "integrity": "sha512-mADw7MS0bYe2OGKkHYMaqarOXuDwRbO6ArD91XhHcl2ynjGCFF+hvqf0LyQcYxkA1zaWjefSkU7Ne9mqgApSgQ==",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/querystring-builder": "^4.0.4",
@@ -2673,7 +2546,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.0.4.tgz",
       "integrity": "sha512-qnbTPUhCVnCgBp4z4BUJUhOEkVwxiEi1cyFM+Zj6o+aY8OFGxUQleKWq8ltgp3dujuhXojIvJWdoqpm6dVO3lQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -2688,7 +2560,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.0.4.tgz",
       "integrity": "sha512-bNYMi7WKTJHu0gn26wg8OscncTt1t2b8KcsZxvOv56XA6cyXtOAAAaNP7+m45xfppXfOatXF3Sb1MNsLUgVLTw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2701,7 +2572,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz",
       "integrity": "sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -2713,7 +2583,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.0.4.tgz",
       "integrity": "sha512-F7gDyfI2BB1Kc+4M6rpuOLne5LOcEknH1n6UQB69qv+HucXBR1rkzXBnQTB2q46sFy1PM/zuSJOB532yc8bg3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -2724,12 +2593,11 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/middleware-endpoint": {
-      "version": "4.1.11",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.11.tgz",
-      "integrity": "sha512-zDogwtRLzKl58lVS8wPcARevFZNBOOqnmzWWxVe9XiaXU2CADFjvJ9XfNibgkOWs08sxLuSr81NrpY4mgp9OwQ==",
-      "license": "Apache-2.0",
+      "version": "4.1.16",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.16.tgz",
+      "integrity": "sha512-plpa50PIGLqzMR2ANKAw2yOW5YKS626KYKqae3atwucbz4Ve4uQ9K9BEZxDLIFmCu7hKLcrq2zmj4a+PfmUV5w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
+        "@smithy/core": "^3.7.1",
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -2743,18 +2611,17 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/middleware-retry": {
-      "version": "4.1.12",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.12.tgz",
-      "integrity": "sha512-wvIH70c4e91NtRxdaLZF+mbLZ/HcC6yg7ySKUiufL6ESp6zJUSnJucZ309AvG9nqCFHSRB5I6T3Ez1Q9wCh0Ww==",
-      "license": "Apache-2.0",
+      "version": "4.1.17",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.17.tgz",
+      "integrity": "sha512-gsCimeG6BApj0SBecwa1Be+Z+JOJe46iy3B3m3A8jKJHf7eIihP76Is4LwLrbJ1ygoS7Vg73lfqzejmLOrazUA==",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/protocol-http": "^5.1.2",
-        "@smithy/service-error-classification": "^4.0.5",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/service-error-classification": "^4.0.6",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-retry": "^4.0.5",
+        "@smithy/util-retry": "^4.0.6",
         "tslib": "^2.6.2",
         "uuid": "^9.0.1"
       },
@@ -2766,7 +2633,6 @@
       "version": "4.0.8",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.0.8.tgz",
       "integrity": "sha512-iSSl7HJoJaGyMIoNn2B7czghOVwJ9nD7TMvLhMWeSB5vt0TnEYyRRqPJu/TqW76WScaNvYYB8nRoiBHR9S1Ddw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -2780,7 +2646,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.0.4.tgz",
       "integrity": "sha512-kagK5ggDrBUCCzI93ft6DjteNSfY8Ulr83UtySog/h09lTIOAJ/xUSObutanlPT0nhoHAkpmW9V5K8oPyLh+QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2793,7 +2658,6 @@
       "version": "4.1.3",
       "resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.1.3.tgz",
       "integrity": "sha512-HGHQr2s59qaU1lrVH6MbLlmOBxadtzTsoO4c+bF5asdgVik3I8o7JIOzoeqWc5MjVa+vD36/LWE0iXKpNqooRw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -2805,10 +2669,9 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/node-http-handler": {
-      "version": "4.0.6",
-      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.0.6.tgz",
-      "integrity": "sha512-NqbmSz7AW2rvw4kXhKGrYTiJVDHnMsFnX4i+/FzcZAfbOBauPYs2ekuECkSbtqaxETLLTu9Rl/ex6+I2BKErPA==",
-      "license": "Apache-2.0",
+      "version": "4.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.1.0.tgz",
+      "integrity": "sha512-vqfSiHz2v8b3TTTrdXi03vNz1KLYYS3bhHCDv36FYDqxT7jvTll1mMnCrkD+gOvgwybuunh/2VmvOMqwBegxEg==",
       "dependencies": {
         "@smithy/abort-controller": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
@@ -2824,7 +2687,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.4.tgz",
       "integrity": "sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2837,7 +2699,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.1.2.tgz",
       "integrity": "sha512-rOG5cNLBXovxIrICSBm95dLqzfvxjEmuZx4KK3hWwPFHGdW3lxY0fZNXfv2zebfRO7sJZ5pKJYHScsqopeIWtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2850,7 +2711,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.0.4.tgz",
       "integrity": "sha512-SwREZcDnEYoh9tLNgMbpop+UTGq44Hl9tdj3rf+yeLcfH7+J8OXEBaMc2kDxtyRHu8BhSg9ADEx0gFHvpJgU8w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-uri-escape": "^4.0.0",
@@ -2864,7 +2724,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.0.4.tgz",
       "integrity": "sha512-6yZf53i/qB8gRHH/l2ZwUG5xgkPgQF15/KxH0DdXMDHjesA9MeZje/853ifkSY0x4m5S+dfDZ+c4x439PF0M2w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2874,10 +2733,9 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/service-error-classification": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.5.tgz",
-      "integrity": "sha512-LvcfhrnCBvCmTee81pRlh1F39yTS/+kYleVeLCwNtkY8wtGg8V/ca9rbZZvYIl8OjlMtL6KIjaiL/lgVqHD2nA==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.6.tgz",
+      "integrity": "sha512-RRoTDL//7xi4tn5FrN2NzH17jbgmnKidUqd4KvquT0954/i6CXXkh1884jBiunq24g9cGtPBEXlU40W6EpNOOg==",
       "dependencies": {
         "@smithy/types": "^4.3.1"
       },
@@ -2889,7 +2747,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.4.tgz",
       "integrity": "sha512-63X0260LoFBjrHifPDs+nM9tV0VMkOTl4JRMYNuKh/f5PauSjowTfvF3LogfkWdcPoxsA9UjqEOgjeYIbhb7Nw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -2902,7 +2759,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.1.2.tgz",
       "integrity": "sha512-d3+U/VpX7a60seHziWnVZOHuEgJlclufjkS6zhXvxcJgkJq4UWdH5eOBLzHRMx6gXjsdT9h6lfpmLzbrdupHgQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -2918,17 +2774,16 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/smithy-client": {
-      "version": "4.4.3",
-      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.3.tgz",
-      "integrity": "sha512-xxzNYgA0HD6ETCe5QJubsxP0hQH3QK3kbpJz3QrosBCuIWyEXLR/CO5hFb2OeawEKUxMNhz3a1nuJNN2np2RMA==",
-      "license": "Apache-2.0",
+      "version": "4.4.8",
+      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.8.tgz",
+      "integrity": "sha512-pcW691/lx7V54gE+dDGC26nxz8nrvnvRSCJaIYD6XLPpOInEZeKdV/SpSux+wqeQ4Ine7LJQu8uxMvobTIBK0w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
-        "@smithy/middleware-endpoint": "^4.1.11",
+        "@smithy/core": "^3.7.1",
+        "@smithy/middleware-endpoint": "^4.1.16",
         "@smithy/middleware-stack": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "tslib": "^2.6.2"
       },
       "engines": {
@@ -2939,7 +2794,6 @@
       "version": "4.3.1",
       "resolved": "https://registry.npmjs.org/@smithy/types/-/types-4.3.1.tgz",
       "integrity": "sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -2951,7 +2805,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.0.4.tgz",
       "integrity": "sha512-eMkc144MuN7B0TDA4U2fKs+BqczVbk3W+qIvcoCY6D1JY3hnAdCuhCZODC+GAeaxj0p6Jroz4+XMUn3PCxQQeQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/querystring-parser": "^4.0.4",
         "@smithy/types": "^4.3.1",
@@ -2965,7 +2818,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.0.0.tgz",
       "integrity": "sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "@smithy/util-utf8": "^4.0.0",
@@ -2979,7 +2831,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz",
       "integrity": "sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -2991,7 +2842,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz",
       "integrity": "sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -3003,7 +2853,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz",
       "integrity": "sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "tslib": "^2.6.2"
@@ -3016,7 +2865,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz",
       "integrity": "sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -3025,13 +2873,12 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/util-defaults-mode-browser": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.19.tgz",
-      "integrity": "sha512-mvLMh87xSmQrV5XqnUYEPoiFFeEGYeAKIDDKdhE2ahqitm8OHM3aSvhqL6rrK6wm1brIk90JhxDf5lf2hbrLbQ==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.24.tgz",
+      "integrity": "sha512-UkQNgaQ+bidw1MgdgPO1z1k95W/v8Ej/5o/T/Is8PiVUYPspl/ZxV6WO/8DrzZQu5ULnmpB9CDdMSRwgRc21AA==",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "bowser": "^2.11.0",
         "tslib": "^2.6.2"
@@ -3041,16 +2888,15 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/util-defaults-mode-node": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.19.tgz",
-      "integrity": "sha512-8tYnx+LUfj6m+zkUUIrIQJxPM1xVxfRBvoGHua7R/i6qAxOMjqR6CpEpDwKoIs1o0+hOjGvkKE23CafKL0vJ9w==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.24.tgz",
+      "integrity": "sha512-phvGi/15Z4MpuQibTLOYIumvLdXb+XIJu8TA55voGgboln85jytA3wiD7CkUE8SNcWqkkb+uptZKPiuFouX/7g==",
       "dependencies": {
         "@smithy/config-resolver": "^4.1.4",
         "@smithy/credential-provider-imds": "^4.0.6",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -3062,7 +2908,6 @@
       "version": "3.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.0.6.tgz",
       "integrity": "sha512-YARl3tFL3WgPuLzljRUnrS2ngLiUtkwhQtj8PAL13XZSyUiNLQxwG3fBBq3QXFqGFUXepIN73pINp3y8c2nBmA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -3076,7 +2921,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz",
       "integrity": "sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -3088,7 +2932,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.0.4.tgz",
       "integrity": "sha512-9MLKmkBmf4PRb0ONJikCbCwORACcil6gUWojwARCClT7RmLzF04hUR4WdRprIXal7XVyrddadYNfp2eF3nrvtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3098,12 +2941,11 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/util-retry": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.5.tgz",
-      "integrity": "sha512-V7MSjVDTlEt/plmOFBn1762Dyu5uqMrV2Pl2X0dYk4XvWfdWJNe9Bs5Bzb56wkCuiWjSfClVMGcsuKrGj7S/yg==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.6.tgz",
+      "integrity": "sha512-+YekoF2CaSMv6zKrA6iI/N9yva3Gzn4L6n35Luydweu5MMPYpiGZlWqehPHDHyNbnyaYlz/WJyYAZnC+loBDZg==",
       "dependencies": {
-        "@smithy/service-error-classification": "^4.0.5",
+        "@smithy/service-error-classification": "^4.0.6",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -3112,13 +2954,12 @@
       }
     },
     "node_modules/@aws-sdk/credential-providers/node_modules/@smithy/util-stream": {
-      "version": "4.2.2",
-      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.2.tgz",
-      "integrity": "sha512-aI+GLi7MJoVxg24/3J1ipwLoYzgkB4kUfogZfnslcYlynj3xsQ0e7vk4TnTro9hhsS5PvX1mwmkRqqHQjwcU7w==",
-      "license": "Apache-2.0",
+      "version": "4.2.3",
+      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.3.tgz",
+      "integrity": "sha512-cQn412DWHHFNKrQfbHY8vSFI3nTROY1aIKji9N0tpp8gUABRilr7wdf8fqBbSlXresobM+tQFNk6I+0LXK/YZg==",
       "dependencies": {
-        "@smithy/fetch-http-handler": "^5.0.4",
-        "@smithy/node-http-handler": "^4.0.6",
+        "@smithy/fetch-http-handler": "^5.1.0",
+        "@smithy/node-http-handler": "^4.1.0",
         "@smithy/types": "^4.3.1",
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -3134,7 +2975,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz",
       "integrity": "sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -3146,7 +2986,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.0.0.tgz",
       "integrity": "sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "tslib": "^2.6.2"
@@ -3155,19 +2994,6 @@
         "node": ">=18.0.0"
       }
     },
-    "node_modules/@aws-sdk/credential-providers/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "license": "MIT",
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@aws-sdk/endpoint-cache": {
       "version": "3.693.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/endpoint-cache/-/endpoint-cache-3.693.0.tgz",
@@ -3567,7 +3393,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/nested-clients/-/nested-clients-3.828.0.tgz",
       "integrity": "sha512-xmeOILiR9LvfC8MctgeRXXN8nQTwbOvO4wHvgE8tDRsjnBpyyO0j50R4+viHXdMUGtgGkHEXRv8fFNBq54RgnA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-crypto/sha256-browser": "5.2.0",
         "@aws-crypto/sha256-js": "5.2.0",
@@ -3616,7 +3441,6 @@
       "version": "3.826.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/core/-/core-3.826.0.tgz",
       "integrity": "sha512-BGbQYzWj3ps+dblq33FY5tz/SsgJCcXX0zjQlSC07tYvU1jHTUvsefphyig+fY38xZ4wdKjbTop+KUmXUYrOXw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@aws-sdk/xml-builder": "3.821.0",
@@ -3642,7 +3466,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-host-header/-/middleware-host-header-3.821.0.tgz",
       "integrity": "sha512-xSMR+sopSeWGx5/4pAGhhfMvGBHioVBbqGvDs6pG64xfNwM5vq5s5v6D04e2i+uSTj4qGa71dLUs5I0UzAK3sw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -3657,7 +3480,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-logger/-/middleware-logger-3.821.0.tgz",
       "integrity": "sha512-0cvI0ipf2tGx7fXYEEN5fBeZDz2RnHyb9xftSgUsEq7NBxjV0yTZfLJw6Za5rjE6snC80dRN8+bTNR1tuG89zA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -3671,7 +3493,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-recursion-detection/-/middleware-recursion-detection-3.821.0.tgz",
       "integrity": "sha512-efmaifbhBoqKG3bAoEfDdcM8hn1psF+4qa7ykWuYmfmah59JBeqHLfz5W9m9JoTwoKPkFcVLWZxnyZzAnVBOIg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -3686,7 +3507,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/middleware-user-agent/-/middleware-user-agent-3.828.0.tgz",
       "integrity": "sha512-nixvI/SETXRdmrVab4D9LvXT3lrXkwAWGWk2GVvQvzlqN1/M/RfClj+o37Sn4FqRkGH9o9g7Fqb1YqZ4mqDAtA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/core": "3.826.0",
         "@aws-sdk/types": "3.821.0",
@@ -3704,7 +3524,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.821.0.tgz",
       "integrity": "sha512-t8og+lRCIIy5nlId0bScNpCkif8sc0LhmtaKsbm0ZPm3sCa/WhCbSZibjbZ28FNjVCV+p0D9RYZx0VDDbtWyjw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/node-config-provider": "^4.1.3",
@@ -3721,7 +3540,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/types/-/types-3.821.0.tgz",
       "integrity": "sha512-Znroqdai1a90TlxGaJ+FK1lwC0fHpo97Xjsp5UKGR5JODYm7f9+/fF17ebO1KdoBr/Rm0UIFiF5VmI8ts9F1eA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3734,7 +3552,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-endpoints/-/util-endpoints-3.828.0.tgz",
       "integrity": "sha512-RvKch111SblqdkPzg3oCIdlGxlQs+k+P7Etory9FmxPHyPDvsP1j1c74PmgYqtzzMWmoXTjd+c9naUHh9xG8xg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -3749,7 +3566,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-browser/-/util-user-agent-browser-3.821.0.tgz",
       "integrity": "sha512-irWZHyM0Jr1xhC+38OuZ7JB6OXMLPZlj48thElpsO1ZSLRkLZx5+I7VV6k3sp2yZ7BYbKz/G2ojSv4wdm7XTLw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/types": "3.821.0",
         "@smithy/types": "^4.3.1",
@@ -3761,7 +3577,6 @@
       "version": "3.828.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/util-user-agent-node/-/util-user-agent-node-3.828.0.tgz",
       "integrity": "sha512-LdN6fTBzTlQmc8O8f1wiZN0qF3yBWVGis7NwpWK7FUEzP9bEZRxYfIkV9oV9zpt6iNRze1SedK3JQVB/udxBoA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@aws-sdk/middleware-user-agent": "3.828.0",
         "@aws-sdk/types": "3.821.0",
@@ -3785,7 +3600,6 @@
       "version": "3.821.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.821.0.tgz",
       "integrity": "sha512-DIIotRnefVL6DiaHtO6/21DhJ4JZnnIwdNbpwiAhdt/AVbttcE4yw925gsjur0OGv5BTYXQXU3YnANBYnZjuQA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3798,7 +3612,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/abort-controller/-/abort-controller-4.0.4.tgz",
       "integrity": "sha512-gJnEjZMvigPDQWHrW3oPrFhQtkrgqBkyjj3pCIdF3A5M6vsZODG93KNlfJprv6bp4245bdT32fsHK4kkH3KYDA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3811,7 +3624,6 @@
       "version": "4.1.4",
       "resolved": "https://registry.npmjs.org/@smithy/config-resolver/-/config-resolver-4.1.4.tgz",
       "integrity": "sha512-prmU+rDddxHOH0oNcwemL+SwnzcG65sBF2yXRO7aeXIn/xTlq2pX7JLVbkBnVLowHLg4/OL4+jBmv9hVrVGS+w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -3824,10 +3636,9 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/core": {
-      "version": "3.5.3",
-      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.5.3.tgz",
-      "integrity": "sha512-xa5byV9fEguZNofCclv6v9ra0FYh5FATQW/da7FQUVTic94DfrN/NvmKZjrMyzbpqfot9ZjBaO8U1UeTbmSLuA==",
-      "license": "Apache-2.0",
+      "version": "3.7.1",
+      "resolved": "https://registry.npmjs.org/@smithy/core/-/core-3.7.1.tgz",
+      "integrity": "sha512-ExRCsHnXFtBPnM7MkfKBPcBBdHw1h/QS/cbNw4ho95qnyNHvnpmGbR39MIAv9KggTr5qSPxRSEL+hRXlyGyGQw==",
       "dependencies": {
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/protocol-http": "^5.1.2",
@@ -3835,7 +3646,7 @@
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-body-length-browser": "^4.0.0",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "@smithy/util-utf8": "^4.0.0",
         "tslib": "^2.6.2"
       },
@@ -3847,7 +3658,6 @@
       "version": "4.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/credential-provider-imds/-/credential-provider-imds-4.0.6.tgz",
       "integrity": "sha512-hKMWcANhUiNbCJouYkZ9V3+/Qf9pteR1dnwgdyzR09R4ODEYx8BbUysHwRSyex4rZ9zapddZhLFTnT4ZijR4pw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
@@ -3860,10 +3670,9 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/fetch-http-handler": {
-      "version": "5.0.4",
-      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.0.4.tgz",
-      "integrity": "sha512-AMtBR5pHppYMVD7z7G+OlHHAcgAN7v0kVKEpHuTO4Gb199Gowh0taYi9oDStFeUhetkeP55JLSVlTW1n9rFtUw==",
-      "license": "Apache-2.0",
+      "version": "5.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-5.1.0.tgz",
+      "integrity": "sha512-mADw7MS0bYe2OGKkHYMaqarOXuDwRbO6ArD91XhHcl2ynjGCFF+hvqf0LyQcYxkA1zaWjefSkU7Ne9mqgApSgQ==",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/querystring-builder": "^4.0.4",
@@ -3879,7 +3688,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/hash-node/-/hash-node-4.0.4.tgz",
       "integrity": "sha512-qnbTPUhCVnCgBp4z4BUJUhOEkVwxiEi1cyFM+Zj6o+aY8OFGxUQleKWq8ltgp3dujuhXojIvJWdoqpm6dVO3lQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -3894,7 +3702,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/invalid-dependency/-/invalid-dependency-4.0.4.tgz",
       "integrity": "sha512-bNYMi7WKTJHu0gn26wg8OscncTt1t2b8KcsZxvOv56XA6cyXtOAAAaNP7+m45xfppXfOatXF3Sb1MNsLUgVLTw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3907,7 +3714,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/is-array-buffer/-/is-array-buffer-4.0.0.tgz",
       "integrity": "sha512-saYhF8ZZNoJDTvJBEWgeBccCg+yvp1CX+ed12yORU3NilJScfc6gfch2oVb4QgxZrGUx3/ZJlb+c/dJbyupxlw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -3919,7 +3725,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-content-length/-/middleware-content-length-4.0.4.tgz",
       "integrity": "sha512-F7gDyfI2BB1Kc+4M6rpuOLne5LOcEknH1n6UQB69qv+HucXBR1rkzXBnQTB2q46sFy1PM/zuSJOB532yc8bg3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -3930,12 +3735,11 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/middleware-endpoint": {
-      "version": "4.1.11",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.11.tgz",
-      "integrity": "sha512-zDogwtRLzKl58lVS8wPcARevFZNBOOqnmzWWxVe9XiaXU2CADFjvJ9XfNibgkOWs08sxLuSr81NrpY4mgp9OwQ==",
-      "license": "Apache-2.0",
+      "version": "4.1.16",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-endpoint/-/middleware-endpoint-4.1.16.tgz",
+      "integrity": "sha512-plpa50PIGLqzMR2ANKAw2yOW5YKS626KYKqae3atwucbz4Ve4uQ9K9BEZxDLIFmCu7hKLcrq2zmj4a+PfmUV5w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
+        "@smithy/core": "^3.7.1",
         "@smithy/middleware-serde": "^4.0.8",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -3949,18 +3753,17 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/middleware-retry": {
-      "version": "4.1.12",
-      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.12.tgz",
-      "integrity": "sha512-wvIH70c4e91NtRxdaLZF+mbLZ/HcC6yg7ySKUiufL6ESp6zJUSnJucZ309AvG9nqCFHSRB5I6T3Ez1Q9wCh0Ww==",
-      "license": "Apache-2.0",
+      "version": "4.1.17",
+      "resolved": "https://registry.npmjs.org/@smithy/middleware-retry/-/middleware-retry-4.1.17.tgz",
+      "integrity": "sha512-gsCimeG6BApj0SBecwa1Be+Z+JOJe46iy3B3m3A8jKJHf7eIihP76Is4LwLrbJ1ygoS7Vg73lfqzejmLOrazUA==",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/protocol-http": "^5.1.2",
-        "@smithy/service-error-classification": "^4.0.5",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/service-error-classification": "^4.0.6",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "@smithy/util-middleware": "^4.0.4",
-        "@smithy/util-retry": "^4.0.5",
+        "@smithy/util-retry": "^4.0.6",
         "tslib": "^2.6.2",
         "uuid": "^9.0.1"
       },
@@ -3972,7 +3775,6 @@
       "version": "4.0.8",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-4.0.8.tgz",
       "integrity": "sha512-iSSl7HJoJaGyMIoNn2B7czghOVwJ9nD7TMvLhMWeSB5vt0TnEYyRRqPJu/TqW76WScaNvYYB8nRoiBHR9S1Ddw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
@@ -3986,7 +3788,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-stack/-/middleware-stack-4.0.4.tgz",
       "integrity": "sha512-kagK5ggDrBUCCzI93ft6DjteNSfY8Ulr83UtySog/h09lTIOAJ/xUSObutanlPT0nhoHAkpmW9V5K8oPyLh+QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -3999,7 +3800,6 @@
       "version": "4.1.3",
       "resolved": "https://registry.npmjs.org/@smithy/node-config-provider/-/node-config-provider-4.1.3.tgz",
       "integrity": "sha512-HGHQr2s59qaU1lrVH6MbLlmOBxadtzTsoO4c+bF5asdgVik3I8o7JIOzoeqWc5MjVa+vD36/LWE0iXKpNqooRw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
         "@smithy/shared-ini-file-loader": "^4.0.4",
@@ -4011,10 +3811,9 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/node-http-handler": {
-      "version": "4.0.6",
-      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.0.6.tgz",
-      "integrity": "sha512-NqbmSz7AW2rvw4kXhKGrYTiJVDHnMsFnX4i+/FzcZAfbOBauPYs2ekuECkSbtqaxETLLTu9Rl/ex6+I2BKErPA==",
-      "license": "Apache-2.0",
+      "version": "4.1.0",
+      "resolved": "https://registry.npmjs.org/@smithy/node-http-handler/-/node-http-handler-4.1.0.tgz",
+      "integrity": "sha512-vqfSiHz2v8b3TTTrdXi03vNz1KLYYS3bhHCDv36FYDqxT7jvTll1mMnCrkD+gOvgwybuunh/2VmvOMqwBegxEg==",
       "dependencies": {
         "@smithy/abort-controller": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
@@ -4030,7 +3829,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/property-provider/-/property-provider-4.0.4.tgz",
       "integrity": "sha512-qHJ2sSgu4FqF4U/5UUp4DhXNmdTrgmoAai6oQiM+c5RZ/sbDwJ12qxB1M6FnP+Tn/ggkPZf9ccn4jqKSINaquw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -4043,7 +3841,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/protocol-http/-/protocol-http-5.1.2.tgz",
       "integrity": "sha512-rOG5cNLBXovxIrICSBm95dLqzfvxjEmuZx4KK3hWwPFHGdW3lxY0fZNXfv2zebfRO7sJZ5pKJYHScsqopeIWtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -4056,7 +3853,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-builder/-/querystring-builder-4.0.4.tgz",
       "integrity": "sha512-SwREZcDnEYoh9tLNgMbpop+UTGq44Hl9tdj3rf+yeLcfH7+J8OXEBaMc2kDxtyRHu8BhSg9ADEx0gFHvpJgU8w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "@smithy/util-uri-escape": "^4.0.0",
@@ -4070,7 +3866,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/querystring-parser/-/querystring-parser-4.0.4.tgz",
       "integrity": "sha512-6yZf53i/qB8gRHH/l2ZwUG5xgkPgQF15/KxH0DdXMDHjesA9MeZje/853ifkSY0x4m5S+dfDZ+c4x439PF0M2w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -4080,10 +3875,9 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/service-error-classification": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.5.tgz",
-      "integrity": "sha512-LvcfhrnCBvCmTee81pRlh1F39yTS/+kYleVeLCwNtkY8wtGg8V/ca9rbZZvYIl8OjlMtL6KIjaiL/lgVqHD2nA==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/service-error-classification/-/service-error-classification-4.0.6.tgz",
+      "integrity": "sha512-RRoTDL//7xi4tn5FrN2NzH17jbgmnKidUqd4KvquT0954/i6CXXkh1884jBiunq24g9cGtPBEXlU40W6EpNOOg==",
       "dependencies": {
         "@smithy/types": "^4.3.1"
       },
@@ -4095,7 +3889,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/shared-ini-file-loader/-/shared-ini-file-loader-4.0.4.tgz",
       "integrity": "sha512-63X0260LoFBjrHifPDs+nM9tV0VMkOTl4JRMYNuKh/f5PauSjowTfvF3LogfkWdcPoxsA9UjqEOgjeYIbhb7Nw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -4108,7 +3901,6 @@
       "version": "5.1.2",
       "resolved": "https://registry.npmjs.org/@smithy/signature-v4/-/signature-v4-5.1.2.tgz",
       "integrity": "sha512-d3+U/VpX7a60seHziWnVZOHuEgJlclufjkS6zhXvxcJgkJq4UWdH5eOBLzHRMx6gXjsdT9h6lfpmLzbrdupHgQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "@smithy/protocol-http": "^5.1.2",
@@ -4124,17 +3916,16 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/smithy-client": {
-      "version": "4.4.3",
-      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.3.tgz",
-      "integrity": "sha512-xxzNYgA0HD6ETCe5QJubsxP0hQH3QK3kbpJz3QrosBCuIWyEXLR/CO5hFb2OeawEKUxMNhz3a1nuJNN2np2RMA==",
-      "license": "Apache-2.0",
+      "version": "4.4.8",
+      "resolved": "https://registry.npmjs.org/@smithy/smithy-client/-/smithy-client-4.4.8.tgz",
+      "integrity": "sha512-pcW691/lx7V54gE+dDGC26nxz8nrvnvRSCJaIYD6XLPpOInEZeKdV/SpSux+wqeQ4Ine7LJQu8uxMvobTIBK0w==",
       "dependencies": {
-        "@smithy/core": "^3.5.3",
-        "@smithy/middleware-endpoint": "^4.1.11",
+        "@smithy/core": "^3.7.1",
+        "@smithy/middleware-endpoint": "^4.1.16",
         "@smithy/middleware-stack": "^4.0.4",
         "@smithy/protocol-http": "^5.1.2",
         "@smithy/types": "^4.3.1",
-        "@smithy/util-stream": "^4.2.2",
+        "@smithy/util-stream": "^4.2.3",
         "tslib": "^2.6.2"
       },
       "engines": {
@@ -4145,7 +3936,6 @@
       "version": "4.3.1",
       "resolved": "https://registry.npmjs.org/@smithy/types/-/types-4.3.1.tgz",
       "integrity": "sha512-UqKOQBL2x6+HWl3P+3QqFD4ncKq0I8Nuz9QItGv5WuKuMHuuwlhvqcZCoXGfc+P1QmfJE7VieykoYYmrOoFJxA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4157,7 +3947,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/url-parser/-/url-parser-4.0.4.tgz",
       "integrity": "sha512-eMkc144MuN7B0TDA4U2fKs+BqczVbk3W+qIvcoCY6D1JY3hnAdCuhCZODC+GAeaxj0p6Jroz4+XMUn3PCxQQeQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/querystring-parser": "^4.0.4",
         "@smithy/types": "^4.3.1",
@@ -4171,7 +3960,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-base64/-/util-base64-4.0.0.tgz",
       "integrity": "sha512-CvHfCmO2mchox9kjrtzoHkWHxjHZzaFojLc8quxXY7WAAMAg43nuxwv95tATVgQFNDwd4M9S1qFzj40Ul41Kmg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "@smithy/util-utf8": "^4.0.0",
@@ -4185,7 +3973,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-browser/-/util-body-length-browser-4.0.0.tgz",
       "integrity": "sha512-sNi3DL0/k64/LO3A256M+m3CDdG6V7WKWHdAiBBMUN8S3hK3aMPhwnPik2A/a2ONN+9doY9UxaLfgqsIRg69QA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4197,7 +3984,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-body-length-node/-/util-body-length-node-4.0.0.tgz",
       "integrity": "sha512-q0iDP3VsZzqJyje8xJWEJCNIu3lktUGVoSy1KB0UWym2CL1siV3artm+u1DFYTLejpsrdGyCSWBdGNjJzfDPjg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4209,7 +3995,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-buffer-from/-/util-buffer-from-4.0.0.tgz",
       "integrity": "sha512-9TOQ7781sZvddgO8nxueKi3+yGvkY35kotA0Y6BWRajAv8jjmigQ1sBwz0UX47pQMYXJPahSKEKYFgt+rXdcug==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/is-array-buffer": "^4.0.0",
         "tslib": "^2.6.2"
@@ -4222,7 +4007,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-config-provider/-/util-config-provider-4.0.0.tgz",
       "integrity": "sha512-L1RBVzLyfE8OXH+1hsJ8p+acNUSirQnWQ6/EgpchV88G6zGBTDPdXiiExei6Z1wR2RxYvxY/XLw6AMNCCt8H3w==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4231,13 +4015,12 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/util-defaults-mode-browser": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.19.tgz",
-      "integrity": "sha512-mvLMh87xSmQrV5XqnUYEPoiFFeEGYeAKIDDKdhE2ahqitm8OHM3aSvhqL6rrK6wm1brIk90JhxDf5lf2hbrLbQ==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-browser/-/util-defaults-mode-browser-4.0.24.tgz",
+      "integrity": "sha512-UkQNgaQ+bidw1MgdgPO1z1k95W/v8Ej/5o/T/Is8PiVUYPspl/ZxV6WO/8DrzZQu5ULnmpB9CDdMSRwgRc21AA==",
       "dependencies": {
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "bowser": "^2.11.0",
         "tslib": "^2.6.2"
@@ -4247,16 +4030,15 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/util-defaults-mode-node": {
-      "version": "4.0.19",
-      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.19.tgz",
-      "integrity": "sha512-8tYnx+LUfj6m+zkUUIrIQJxPM1xVxfRBvoGHua7R/i6qAxOMjqR6CpEpDwKoIs1o0+hOjGvkKE23CafKL0vJ9w==",
-      "license": "Apache-2.0",
+      "version": "4.0.24",
+      "resolved": "https://registry.npmjs.org/@smithy/util-defaults-mode-node/-/util-defaults-mode-node-4.0.24.tgz",
+      "integrity": "sha512-phvGi/15Z4MpuQibTLOYIumvLdXb+XIJu8TA55voGgboln85jytA3wiD7CkUE8SNcWqkkb+uptZKPiuFouX/7g==",
       "dependencies": {
         "@smithy/config-resolver": "^4.1.4",
         "@smithy/credential-provider-imds": "^4.0.6",
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/property-provider": "^4.0.4",
-        "@smithy/smithy-client": "^4.4.3",
+        "@smithy/smithy-client": "^4.4.8",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -4268,7 +4050,6 @@
       "version": "3.0.6",
       "resolved": "https://registry.npmjs.org/@smithy/util-endpoints/-/util-endpoints-3.0.6.tgz",
       "integrity": "sha512-YARl3tFL3WgPuLzljRUnrS2ngLiUtkwhQtj8PAL13XZSyUiNLQxwG3fBBq3QXFqGFUXepIN73pINp3y8c2nBmA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/node-config-provider": "^4.1.3",
         "@smithy/types": "^4.3.1",
@@ -4282,7 +4063,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-4.0.0.tgz",
       "integrity": "sha512-Yk5mLhHtfIgW2W2WQZWSg5kuMZCVbvhFmC7rV4IO2QqnZdbEFPmQnCcGMAX2z/8Qj3B9hYYNjZOhWym+RwhePw==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4294,7 +4074,6 @@
       "version": "4.0.4",
       "resolved": "https://registry.npmjs.org/@smithy/util-middleware/-/util-middleware-4.0.4.tgz",
       "integrity": "sha512-9MLKmkBmf4PRb0ONJikCbCwORACcil6gUWojwARCClT7RmLzF04hUR4WdRprIXal7XVyrddadYNfp2eF3nrvtQ==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
@@ -4304,12 +4083,11 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/util-retry": {
-      "version": "4.0.5",
-      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.5.tgz",
-      "integrity": "sha512-V7MSjVDTlEt/plmOFBn1762Dyu5uqMrV2Pl2X0dYk4XvWfdWJNe9Bs5Bzb56wkCuiWjSfClVMGcsuKrGj7S/yg==",
-      "license": "Apache-2.0",
+      "version": "4.0.6",
+      "resolved": "https://registry.npmjs.org/@smithy/util-retry/-/util-retry-4.0.6.tgz",
+      "integrity": "sha512-+YekoF2CaSMv6zKrA6iI/N9yva3Gzn4L6n35Luydweu5MMPYpiGZlWqehPHDHyNbnyaYlz/WJyYAZnC+loBDZg==",
       "dependencies": {
-        "@smithy/service-error-classification": "^4.0.5",
+        "@smithy/service-error-classification": "^4.0.6",
         "@smithy/types": "^4.3.1",
         "tslib": "^2.6.2"
       },
@@ -4318,13 +4096,12 @@
       }
     },
     "node_modules/@aws-sdk/nested-clients/node_modules/@smithy/util-stream": {
-      "version": "4.2.2",
-      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.2.tgz",
-      "integrity": "sha512-aI+GLi7MJoVxg24/3J1ipwLoYzgkB4kUfogZfnslcYlynj3xsQ0e7vk4TnTro9hhsS5PvX1mwmkRqqHQjwcU7w==",
-      "license": "Apache-2.0",
+      "version": "4.2.3",
+      "resolved": "https://registry.npmjs.org/@smithy/util-stream/-/util-stream-4.2.3.tgz",
+      "integrity": "sha512-cQn412DWHHFNKrQfbHY8vSFI3nTROY1aIKji9N0tpp8gUABRilr7wdf8fqBbSlXresobM+tQFNk6I+0LXK/YZg==",
       "dependencies": {
-        "@smithy/fetch-http-handler": "^5.0.4",
-        "@smithy/node-http-handler": "^4.0.6",
+        "@smithy/fetch-http-handler": "^5.1.0",
+        "@smithy/node-http-handler": "^4.1.0",
         "@smithy/types": "^4.3.1",
         "@smithy/util-base64": "^4.0.0",
         "@smithy/util-buffer-from": "^4.0.0",
@@ -4340,7 +4117,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-uri-escape/-/util-uri-escape-4.0.0.tgz",
       "integrity": "sha512-77yfbCbQMtgtTylO9itEAdpPXSog3ZxMe09AEhm0dU0NLTalV70ghDZFR+Nfi1C60jnJoh/Re4090/DuZh2Omg==",
-      "license": "Apache-2.0",
       "dependencies": {
         "tslib": "^2.6.2"
       },
@@ -4352,7 +4128,6 @@
       "version": "4.0.0",
       "resolved": "https://registry.npmjs.org/@smithy/util-utf8/-/util-utf8-4.0.0.tgz",
       "integrity": "sha512-b+zebfKCfRdgNJDknHCob3O7FpeYQN6ZG6YLExMcasDHsCXlsXCEuiPZeLnJLpwa5dvPetGlnGCiMHuLwGvFow==",
-      "license": "Apache-2.0",
       "dependencies": {
         "@smithy/util-buffer-from": "^4.0.0",
         "tslib": "^2.6.2"
@@ -4361,19 +4136,6 @@
         "node": ">=18.0.0"
       }
     },
-    "node_modules/@aws-sdk/nested-clients/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "license": "MIT",
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@aws-sdk/region-config-resolver": {
       "version": "3.693.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/region-config-resolver/-/region-config-resolver-3.693.0.tgz",
@@ -4562,16 +4324,6 @@
         }
       }
     },
-    "node_modules/@aws-sdk/util-utf8-browser": {
-      "version": "3.259.0",
-      "resolved": "https://registry.npmjs.org/@aws-sdk/util-utf8-browser/-/util-utf8-browser-3.259.0.tgz",
-      "integrity": "sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==",
-      "optional": true,
-      "peer": true,
-      "dependencies": {
-        "tslib": "^2.3.1"
-      }
-    },
     "node_modules/@aws-sdk/xml-builder": {
       "version": "3.693.0",
       "resolved": "https://registry.npmjs.org/@aws-sdk/xml-builder/-/xml-builder-3.693.0.tgz",
@@ -4598,30 +4350,30 @@
       }
     },
     "node_modules/@babel/compat-data": {
-      "version": "7.27.5",
-      "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz",
-      "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==",
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz",
+      "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==",
       "dev": true,
       "engines": {
         "node": ">=6.9.0"
       }
     },
     "node_modules/@babel/core": {
-      "version": "7.27.4",
-      "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz",
-      "integrity": "sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==",
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz",
+      "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==",
       "dev": true,
       "dependencies": {
         "@ampproject/remapping": "^2.2.0",
         "@babel/code-frame": "^7.27.1",
-        "@babel/generator": "^7.27.3",
+        "@babel/generator": "^7.28.0",
         "@babel/helper-compilation-targets": "^7.27.2",
         "@babel/helper-module-transforms": "^7.27.3",
-        "@babel/helpers": "^7.27.4",
-        "@babel/parser": "^7.27.4",
+        "@babel/helpers": "^7.27.6",
+        "@babel/parser": "^7.28.0",
         "@babel/template": "^7.27.2",
-        "@babel/traverse": "^7.27.4",
-        "@babel/types": "^7.27.3",
+        "@babel/traverse": "^7.28.0",
+        "@babel/types": "^7.28.0",
         "convert-source-map": "^2.0.0",
         "debug": "^4.1.0",
         "gensync": "^1.0.0-beta.2",
@@ -4646,15 +4398,15 @@
       }
     },
     "node_modules/@babel/generator": {
-      "version": "7.27.5",
-      "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz",
-      "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==",
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz",
+      "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==",
       "dev": true,
       "dependencies": {
-        "@babel/parser": "^7.27.5",
-        "@babel/types": "^7.27.3",
-        "@jridgewell/gen-mapping": "^0.3.5",
-        "@jridgewell/trace-mapping": "^0.3.25",
+        "@babel/parser": "^7.28.0",
+        "@babel/types": "^7.28.0",
+        "@jridgewell/gen-mapping": "^0.3.12",
+        "@jridgewell/trace-mapping": "^0.3.28",
         "jsesc": "^3.0.2"
       },
       "engines": {
@@ -4686,6 +4438,15 @@
         "semver": "bin/semver.js"
       }
     },
+    "node_modules/@babel/helper-globals": {
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz",
+      "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==",
+      "dev": true,
+      "engines": {
+        "node": ">=6.9.0"
+      }
+    },
     "node_modules/@babel/helper-module-imports": {
       "version": "7.27.1",
       "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz",
@@ -4765,12 +4526,12 @@
       }
     },
     "node_modules/@babel/parser": {
-      "version": "7.27.5",
-      "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz",
-      "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==",
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz",
+      "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==",
       "dev": true,
       "dependencies": {
-        "@babel/types": "^7.27.3"
+        "@babel/types": "^7.28.0"
       },
       "bin": {
         "parser": "bin/babel-parser.js"
@@ -5016,36 +4777,27 @@
       }
     },
     "node_modules/@babel/traverse": {
-      "version": "7.27.4",
-      "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz",
-      "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==",
+      "version": "7.28.0",
+      "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz",
+      "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==",
       "dev": true,
       "dependencies": {
         "@babel/code-frame": "^7.27.1",
-        "@babel/generator": "^7.27.3",
-        "@babel/parser": "^7.27.4",
+        "@babel/generator": "^7.28.0",
+        "@babel/helper-globals": "^7.28.0",
+        "@babel/parser": "^7.28.0",
         "@babel/template": "^7.27.2",
-        "@babel/types": "^7.27.3",
-        "debug": "^4.3.1",
-        "globals": "^11.1.0"
+        "@babel/types": "^7.28.0",
+        "debug": "^4.3.1"
       },
       "engines": {
         "node": ">=6.9.0"
       }
     },
-    "node_modules/@babel/traverse/node_modules/globals": {
-      "version": "11.12.0",
-      "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz",
-      "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==",
-      "dev": true,
-      "engines": {
-        "node": ">=4"
-      }
-    },
     "node_modules/@babel/types": {
-      "version": "7.27.6",
-      "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz",
-      "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==",
+      "version": "7.28.1",
+      "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.1.tgz",
+      "integrity": "sha512-x0LvFTekgSX+83TI28Y9wYPUfzrnl2aT5+5QLnO6v7mSJYtEEevuDRN0F0uSHRk1G1IWZC43o00Y0xDDrpBGPQ==",
       "dev": true,
       "dependencies": {
         "@babel/helper-string-parser": "^7.27.1",
@@ -5585,9 +5337,9 @@
       "dev": true
     },
     "node_modules/@ibm-cloud/watsonx-ai": {
-      "version": "1.6.7",
-      "resolved": "https://registry.npmjs.org/@ibm-cloud/watsonx-ai/-/watsonx-ai-1.6.7.tgz",
-      "integrity": "sha512-lyHG5pjIINc+3fVbodD+ui0kvs7xk6TRAPJasK+8d8+j/FXS6TNsNGjvP79nfQJPTTYYy9IXxFc/3Z4jAqfD7w==",
+      "version": "1.6.8",
+      "resolved": "https://registry.npmjs.org/@ibm-cloud/watsonx-ai/-/watsonx-ai-1.6.8.tgz",
+      "integrity": "sha512-Ip5bLDM40rQRYauRmmIIpxLO57wI3+F59Njmp0hexnVr+uKroV+O9+eAGQkdE2c9d17R16Q77ueAGheZrzqgWA==",
       "peer": true,
       "dependencies": {
         "@types/node": "^18.0.0",
@@ -5599,9 +5351,9 @@
       }
     },
     "node_modules/@ibm-cloud/watsonx-ai/node_modules/@types/node": {
-      "version": "18.19.111",
-      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.111.tgz",
-      "integrity": "sha512-90sGdgA+QLJr1F9X79tQuEut0gEYIfkX9pydI4XGRgvFo9g2JWswefI+WUSUHPYVBHYSEfTEqBxA5hQvAZB3Mw==",
+      "version": "18.19.119",
+      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.119.tgz",
+      "integrity": "sha512-d0F6m9itIPaKnrvEMlzE48UjwZaAnFW7Jwibacw9MNdqadjKNpUm9tfJYDwmShJmgqcoqYUX3EMKO1+RWiuuNg==",
       "peer": true,
       "dependencies": {
         "undici-types": "~5.26.4"
@@ -6387,17 +6139,13 @@
       "dev": true
     },
     "node_modules/@jridgewell/gen-mapping": {
-      "version": "0.3.8",
-      "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz",
-      "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==",
+      "version": "0.3.12",
+      "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz",
+      "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==",
       "dev": true,
       "dependencies": {
-        "@jridgewell/set-array": "^1.2.1",
-        "@jridgewell/sourcemap-codec": "^1.4.10",
+        "@jridgewell/sourcemap-codec": "^1.5.0",
         "@jridgewell/trace-mapping": "^0.3.24"
-      },
-      "engines": {
-        "node": ">=6.0.0"
       }
     },
     "node_modules/@jridgewell/resolve-uri": {
@@ -6409,19 +6157,10 @@
         "node": ">=6.0.0"
       }
     },
-    "node_modules/@jridgewell/set-array": {
-      "version": "1.2.1",
-      "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz",
-      "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==",
-      "dev": true,
-      "engines": {
-        "node": ">=6.0.0"
-      }
-    },
     "node_modules/@jridgewell/source-map": {
-      "version": "0.3.6",
-      "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz",
-      "integrity": "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==",
+      "version": "0.3.10",
+      "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.10.tgz",
+      "integrity": "sha512-0pPkgz9dY+bijgistcTTJ5mR+ocqRXLuhXHYdzoMmmoJ2C9S46RCm2GMUbatPEUK9Yjy26IrAy8D/M00lLkv+Q==",
       "dev": true,
       "dependencies": {
         "@jridgewell/gen-mapping": "^0.3.5",
@@ -6429,15 +6168,15 @@
       }
     },
     "node_modules/@jridgewell/sourcemap-codec": {
-      "version": "1.5.0",
-      "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz",
-      "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==",
+      "version": "1.5.4",
+      "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz",
+      "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==",
       "dev": true
     },
     "node_modules/@jridgewell/trace-mapping": {
-      "version": "0.3.25",
-      "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz",
-      "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==",
+      "version": "0.3.29",
+      "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz",
+      "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==",
       "dev": true,
       "dependencies": {
         "@jridgewell/resolve-uri": "^3.1.0",
@@ -6960,6 +6699,18 @@
         }
       }
     },
+    "node_modules/@langchain/community/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
     "node_modules/@langchain/core": {
       "version": "0.3.18",
       "resolved": "https://registry.npmjs.org/@langchain/core/-/core-0.3.18.tgz",
@@ -6981,6 +6732,18 @@
         "node": ">=18"
       }
     },
+    "node_modules/@langchain/core/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
     "node_modules/@langchain/openai": {
       "version": "0.3.14",
       "resolved": "https://registry.npmjs.org/@langchain/openai/-/openai-0.3.14.tgz",
@@ -6999,9 +6762,9 @@
       }
     },
     "node_modules/@langchain/openai/node_modules/@types/node": {
-      "version": "18.19.111",
-      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.111.tgz",
-      "integrity": "sha512-90sGdgA+QLJr1F9X79tQuEut0gEYIfkX9pydI4XGRgvFo9g2JWswefI+WUSUHPYVBHYSEfTEqBxA5hQvAZB3Mw==",
+      "version": "18.19.119",
+      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.119.tgz",
+      "integrity": "sha512-d0F6m9itIPaKnrvEMlzE48UjwZaAnFW7Jwibacw9MNdqadjKNpUm9tfJYDwmShJmgqcoqYUX3EMKO1+RWiuuNg==",
       "dependencies": {
         "undici-types": "~5.26.4"
       }
@@ -7447,6 +7210,18 @@
         "@nestjs/core": "^8.0.0 || ^9.0.0 || ^10.0.0"
       }
     },
+    "node_modules/@nestjs/schedule/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
     "node_modules/@nestjs/schematics": {
       "version": "10.2.3",
       "resolved": "https://registry.npmjs.org/@nestjs/schematics/-/schematics-10.2.3.tgz",
@@ -7643,17 +7418,17 @@
       }
     },
     "node_modules/@newrelic/security-agent/node_modules/agent-base": {
-      "version": "7.1.3",
-      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz",
-      "integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==",
+      "version": "7.1.4",
+      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz",
+      "integrity": "sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ==",
       "engines": {
         "node": ">= 14"
       }
     },
     "node_modules/@newrelic/security-agent/node_modules/axios": {
-      "version": "1.9.0",
-      "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz",
-      "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==",
+      "version": "1.10.0",
+      "resolved": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz",
+      "integrity": "sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==",
       "dependencies": {
         "follow-redirects": "^1.15.6",
         "form-data": "^4.0.0",
@@ -7672,18 +7447,6 @@
         "node": ">= 14"
       }
     },
-    "node_modules/@newrelic/security-agent/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@noble/hashes": {
       "version": "1.8.0",
       "resolved": "https://registry.npmjs.org/@noble/hashes/-/hashes-1.8.0.tgz",
@@ -7793,7 +7556,6 @@
       "version": "3.5.1",
       "resolved": "https://registry.npmjs.org/@opensearch-project/opensearch/-/opensearch-3.5.1.tgz",
       "integrity": "sha512-6bf+HcuERzAtHZxrm6phjref54ABse39BpkDie/YO3AUFMCBrb3SK5okKSdT5n3+nDRuEEQLhQCl0RQV3s1qpA==",
-      "license": "Apache-2.0",
       "dependencies": {
         "aws4": "^1.11.0",
         "debug": "^4.3.1",
@@ -8123,9 +7885,9 @@
       }
     },
     "node_modules/@opentelemetry/exporter-metrics-otlp-http/node_modules/@opentelemetry/semantic-conventions": {
-      "version": "1.34.0",
-      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz",
-      "integrity": "sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==",
+      "version": "1.36.0",
+      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.36.0.tgz",
+      "integrity": "sha512-TtxJSRD8Ohxp6bKkhrm27JRHAxPczQA7idtcTOMYI+wQRRrfgqxHv1cFbCApcSnNjtXkmzFozn6jQtFrOmbjPQ==",
       "engines": {
         "node": ">=14"
       }
@@ -8272,9 +8034,9 @@
       }
     },
     "node_modules/@opentelemetry/exporter-metrics-otlp-proto/node_modules/@opentelemetry/semantic-conventions": {
-      "version": "1.34.0",
-      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz",
-      "integrity": "sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==",
+      "version": "1.36.0",
+      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.36.0.tgz",
+      "integrity": "sha512-TtxJSRD8Ohxp6bKkhrm27JRHAxPczQA7idtcTOMYI+wQRRrfgqxHv1cFbCApcSnNjtXkmzFozn6jQtFrOmbjPQ==",
       "engines": {
         "node": ">=14"
       }
@@ -10290,9 +10052,9 @@
       }
     },
     "node_modules/@slack/types": {
-      "version": "2.14.0",
-      "resolved": "https://registry.npmjs.org/@slack/types/-/types-2.14.0.tgz",
-      "integrity": "sha512-n0EGm7ENQRxlXbgKSrQZL69grzg1gHLAVd+GlRVQJ1NSORo0FrApR7wql/gaKdu2n4TO83Sq/AmeUOqD60aXUA==",
+      "version": "2.15.0",
+      "resolved": "https://registry.npmjs.org/@slack/types/-/types-2.15.0.tgz",
+      "integrity": "sha512-livb1gyG3J8ATLBJ3KjZfjHpTRz9btY1m5cgNuXxWJbhwRB1Gwb8Ly6XLJm2Sy1W6h+vLgqIHg7IwKrF1C1Szg==",
       "engines": {
         "node": ">= 12.13.0",
         "npm": ">= 6.12.0"
@@ -10423,80 +10185,14 @@
       }
     },
     "node_modules/@smithy/eventstream-codec": {
-      "version": "2.2.0",
-      "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-2.2.0.tgz",
-      "integrity": "sha512-8janZoJw85nJmQZc4L8TuePp2pk1nxLgkxIR0TUjKJ5Dkj5oelB9WtiSSGXCQvNsJl0VSTvK/2ueMXxvpa9GVw==",
-      "optional": true,
-      "peer": true,
-      "dependencies": {
-        "@aws-crypto/crc32": "3.0.0",
-        "@smithy/types": "^2.12.0",
-        "@smithy/util-hex-encoding": "^2.2.0",
-        "tslib": "^2.6.2"
-      }
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@aws-crypto/crc32": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/@aws-crypto/crc32/-/crc32-3.0.0.tgz",
-      "integrity": "sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==",
-      "optional": true,
-      "peer": true,
-      "dependencies": {
-        "@aws-crypto/util": "^3.0.0",
-        "@aws-sdk/types": "^3.222.0",
-        "tslib": "^1.11.1"
-      }
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@aws-crypto/crc32/node_modules/tslib": {
-      "version": "1.14.1",
-      "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz",
-      "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==",
-      "optional": true,
-      "peer": true
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@aws-crypto/util": {
-      "version": "3.0.0",
-      "resolved": "https://registry.npmjs.org/@aws-crypto/util/-/util-3.0.0.tgz",
-      "integrity": "sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==",
-      "optional": true,
-      "peer": true,
-      "dependencies": {
-        "@aws-sdk/types": "^3.222.0",
-        "@aws-sdk/util-utf8-browser": "^3.0.0",
-        "tslib": "^1.11.1"
-      }
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@aws-crypto/util/node_modules/tslib": {
-      "version": "1.14.1",
-      "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz",
-      "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==",
-      "optional": true,
-      "peer": true
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@smithy/types": {
-      "version": "2.12.0",
-      "resolved": "https://registry.npmjs.org/@smithy/types/-/types-2.12.0.tgz",
-      "integrity": "sha512-QwYgloJ0sVNBeBuBs65cIkTbfzV/Q6ZNPCJ99EICFEdJYG50nGIY/uYXp+TbsdJReIuPr0a0kXmCvren3MbRRw==",
-      "optional": true,
-      "peer": true,
-      "dependencies": {
-        "tslib": "^2.6.2"
-      },
-      "engines": {
-        "node": ">=14.0.0"
-      }
-    },
-    "node_modules/@smithy/eventstream-codec/node_modules/@smithy/util-hex-encoding": {
-      "version": "2.2.0",
-      "resolved": "https://registry.npmjs.org/@smithy/util-hex-encoding/-/util-hex-encoding-2.2.0.tgz",
-      "integrity": "sha512-7iKXR+/4TpLK194pVjKiasIyqMtTYJsgKgM242Y9uzt5dhHnUDvMNb+3xIhRJ9QhvqGii/5cRUt4fJn3dtXNHQ==",
-      "optional": true,
-      "peer": true,
+      "version": "3.1.10",
+      "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-3.1.10.tgz",
+      "integrity": "sha512-323B8YckSbUH0nMIpXn7HZsAVKHYHFUODa8gG9cHo0ySvA1fr5iWaNT+iIL0UCqUzG6QPHA3BSsBtRQou4mMqQ==",
       "dependencies": {
+        "@aws-crypto/crc32": "5.2.0",
+        "@smithy/types": "^3.7.2",
+        "@smithy/util-hex-encoding": "^3.0.0",
         "tslib": "^2.6.2"
-      },
-      "engines": {
-        "node": ">=14.0.0"
       }
     },
     "node_modules/@smithy/eventstream-serde-browser": {
@@ -10550,17 +10246,6 @@
         "node": ">=16.0.0"
       }
     },
-    "node_modules/@smithy/eventstream-serde-universal/node_modules/@smithy/eventstream-codec": {
-      "version": "3.1.10",
-      "resolved": "https://registry.npmjs.org/@smithy/eventstream-codec/-/eventstream-codec-3.1.10.tgz",
-      "integrity": "sha512-323B8YckSbUH0nMIpXn7HZsAVKHYHFUODa8gG9cHo0ySvA1fr5iWaNT+iIL0UCqUzG6QPHA3BSsBtRQou4mMqQ==",
-      "dependencies": {
-        "@aws-crypto/crc32": "5.2.0",
-        "@smithy/types": "^3.7.2",
-        "@smithy/util-hex-encoding": "^3.0.0",
-        "tslib": "^2.6.2"
-      }
-    },
     "node_modules/@smithy/fetch-http-handler": {
       "version": "4.1.3",
       "resolved": "https://registry.npmjs.org/@smithy/fetch-http-handler/-/fetch-http-handler-4.1.3.tgz",
@@ -10763,18 +10448,6 @@
         "node": ">=16.0.0"
       }
     },
-    "node_modules/@smithy/middleware-retry/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/@smithy/middleware-serde": {
       "version": "3.0.11",
       "resolved": "https://registry.npmjs.org/@smithy/middleware-serde/-/middleware-serde-3.0.11.tgz",
@@ -11496,12 +11169,6 @@
         "@types/node": "*"
       }
     },
-    "node_modules/@types/geojson": {
-      "version": "7946.0.16",
-      "resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz",
-      "integrity": "sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==",
-      "dev": true
-    },
     "node_modules/@types/graceful-fs": {
       "version": "4.1.9",
       "resolved": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz",
@@ -11772,9 +11439,9 @@
       "integrity": "sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA=="
     },
     "node_modules/@types/validator": {
-      "version": "13.15.1",
-      "resolved": "https://registry.npmjs.org/@types/validator/-/validator-13.15.1.tgz",
-      "integrity": "sha512-9gG6ogYcoI2mCMLdcO0NYI0AYrbxIjv0MDmy/5Ywo6CpWWrqYayc+mmgxRsCgtcGJm9BSbXkMsmxGah1iGHAAQ=="
+      "version": "13.15.2",
+      "resolved": "https://registry.npmjs.org/@types/validator/-/validator-13.15.2.tgz",
+      "integrity": "sha512-y7pa/oEJJ4iGYBxOpfAKn5b9+xuihvzDVnC/OSvlVnGxVg0pOqmjiMafiJ1KVNQEaPZf9HsEp5icEwGg8uIe5Q=="
     },
     "node_modules/@types/yargs": {
       "version": "17.0.33",
@@ -12367,6 +12034,19 @@
         "acorn": "^8"
       }
     },
+    "node_modules/acorn-import-phases": {
+      "version": "1.0.4",
+      "resolved": "https://registry.npmjs.org/acorn-import-phases/-/acorn-import-phases-1.0.4.tgz",
+      "integrity": "sha512-wKmbr/DDiIXzEOiWrTTUcDm24kQ2vGfZQvM2fwg2vXqR5uW6aapr7ObPtj1th32b9u90/Pf4AItvdTh42fBmVQ==",
+      "dev": true,
+      "peer": true,
+      "engines": {
+        "node": ">=10.13.0"
+      },
+      "peerDependencies": {
+        "acorn": "^8.14.0"
+      }
+    },
     "node_modules/acorn-jsx": {
       "version": "5.3.2",
       "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz",
@@ -12546,9 +12226,9 @@
       "integrity": "sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw=="
     },
     "node_modules/aproba": {
-      "version": "2.0.0",
-      "resolved": "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz",
-      "integrity": "sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ=="
+      "version": "2.1.0",
+      "resolved": "https://registry.npmjs.org/aproba/-/aproba-2.1.0.tgz",
+      "integrity": "sha512-tLIEcj5GuR2RSTnxNKdkK0dJ/GrC7P38sUkiDmDuHfsHmbagTFAxDVIBltoklXEVIQ/f14IL8IMJ5pn9Hez1Ew=="
     },
     "node_modules/are-we-there-yet": {
       "version": "2.0.0",
@@ -12815,8 +12495,7 @@
     "node_modules/aws4": {
       "version": "1.13.2",
       "resolved": "https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz",
-      "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==",
-      "license": "MIT"
+      "integrity": "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw=="
     },
     "node_modules/axios": {
       "version": "1.7.4",
@@ -12840,9 +12519,9 @@
       }
     },
     "node_modules/axios-ntlm/node_modules/axios": {
-      "version": "1.9.0",
-      "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz",
-      "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==",
+      "version": "1.10.0",
+      "resolved": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz",
+      "integrity": "sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==",
       "dependencies": {
         "follow-redirects": "^1.15.6",
         "form-data": "^4.0.0",
@@ -13055,9 +12734,9 @@
       }
     },
     "node_modules/bignumber.js": {
-      "version": "9.3.0",
-      "resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.3.0.tgz",
-      "integrity": "sha512-EM7aMFTXbptt/wZdMlBv2t8IViwQL+h6SLHosp8Yf0dqJMTnY6iL32opnAB6kAdL0SZPuvcAzFr31o0c/R3/RA==",
+      "version": "9.3.1",
+      "resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.3.1.tgz",
+      "integrity": "sha512-Ko0uX15oIUS7wJ3Rb30Fs6SkVbLmPBAKdlm7q9+ak9bbIeFf0MwuBsQV6z7+X768/cHsfg+WlysDWJcmthjsjQ==",
       "engines": {
         "node": "*"
       }
@@ -13160,9 +12839,9 @@
       }
     },
     "node_modules/browserslist": {
-      "version": "4.25.0",
-      "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz",
-      "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==",
+      "version": "4.25.1",
+      "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz",
+      "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==",
       "dev": true,
       "funding": [
         {
@@ -13179,8 +12858,8 @@
         }
       ],
       "dependencies": {
-        "caniuse-lite": "^1.0.30001718",
-        "electron-to-chromium": "^1.5.160",
+        "caniuse-lite": "^1.0.30001726",
+        "electron-to-chromium": "^1.5.173",
         "node-releases": "^2.0.19",
         "update-browserslist-db": "^1.1.3"
       },
@@ -13314,9 +12993,9 @@
       }
     },
     "node_modules/caniuse-lite": {
-      "version": "1.0.30001723",
-      "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz",
-      "integrity": "sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==",
+      "version": "1.0.30001727",
+      "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz",
+      "integrity": "sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==",
       "dev": true,
       "funding": [
         {
@@ -14330,9 +14009,9 @@
       "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
     },
     "node_modules/electron-to-chromium": {
-      "version": "1.5.167",
-      "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.167.tgz",
-      "integrity": "sha512-LxcRvnYO5ez2bMOFpbuuVuAI5QNeY1ncVytE/KXaL6ZNfzX1yPlAO0nSOyIHx2fVAuUprMqPs/TdVhUFZy7SIQ==",
+      "version": "1.5.187",
+      "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.187.tgz",
+      "integrity": "sha512-cl5Jc9I0KGUoOoSbxvTywTa40uspGJt/BDBoDLoxJRSBpWh4FFXBsjNRHfQrONsV/OoEjDfHUmZQa2d6Ze4YgA==",
       "dev": true
     },
     "node_modules/elevenlabs": {
@@ -14396,18 +14075,18 @@
       }
     },
     "node_modules/end-of-stream": {
-      "version": "1.4.4",
-      "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz",
-      "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==",
+      "version": "1.4.5",
+      "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz",
+      "integrity": "sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==",
       "optional": true,
       "dependencies": {
         "once": "^1.4.0"
       }
     },
     "node_modules/enhanced-resolve": {
-      "version": "5.18.1",
-      "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz",
-      "integrity": "sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==",
+      "version": "5.18.2",
+      "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz",
+      "integrity": "sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==",
       "dev": true,
       "dependencies": {
         "graceful-fs": "^4.2.4",
@@ -14716,9 +14395,9 @@
       }
     },
     "node_modules/eslint-module-utils": {
-      "version": "2.12.0",
-      "resolved": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz",
-      "integrity": "sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==",
+      "version": "2.12.1",
+      "resolved": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.1.tgz",
+      "integrity": "sha512-L8jSWTze7K2mTg0vos/RuLRS5soomksDPoJLXIslC7c8Wmut3bx7CPpJijDcBZtxQ5lrbUdM+s0OlNbz0DCDNw==",
       "dev": true,
       "dependencies": {
         "debug": "^3.2.7"
@@ -15772,9 +15451,9 @@
       }
     },
     "node_modules/form-data": {
-      "version": "4.0.3",
-      "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.3.tgz",
-      "integrity": "sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==",
+      "version": "4.0.4",
+      "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz",
+      "integrity": "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow==",
       "dependencies": {
         "asynckit": "^0.4.0",
         "combined-stream": "^1.0.8",
@@ -15983,9 +15662,9 @@
       }
     },
     "node_modules/gaxios/node_modules/agent-base": {
-      "version": "7.1.3",
-      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz",
-      "integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==",
+      "version": "7.1.4",
+      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz",
+      "integrity": "sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ==",
       "engines": {
         "node": ">= 14"
       }
@@ -16002,18 +15681,6 @@
         "node": ">= 14"
       }
     },
-    "node_modules/gaxios/node_modules/uuid": {
-      "version": "9.0.1",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
-      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
-      "funding": [
-        "https://github.com/sponsors/broofa",
-        "https://github.com/sponsors/ctavan"
-      ],
-      "bin": {
-        "uuid": "dist/bin/uuid"
-      }
-    },
     "node_modules/gcp-metadata": {
       "version": "6.1.1",
       "resolved": "https://registry.npmjs.org/gcp-metadata/-/gcp-metadata-6.1.1.tgz",
@@ -16306,6 +15973,11 @@
         "node": ">=8"
       }
     },
+    "node_modules/has-own": {
+      "version": "1.0.1",
+      "resolved": "https://registry.npmjs.org/has-own/-/has-own-1.0.1.tgz",
+      "integrity": "sha512-RDKhzgQTQfMaLvIFhjahU+2gGnRBK6dYOd5Gd9BzkmnBneOCRYjRC003RIMrdAbH52+l+CnMS4bBCXGer8tEhg=="
+    },
     "node_modules/has-own-prop": {
       "version": "2.0.0",
       "resolved": "https://registry.npmjs.org/has-own-prop/-/has-own-prop-2.0.0.tgz",
@@ -16395,7 +16067,6 @@
       "version": "1.2.0",
       "resolved": "https://registry.npmjs.org/hpagent/-/hpagent-1.2.0.tgz",
       "integrity": "sha512-A91dYTeIB6NoXG+PxTQpCCDDnfHsW9kc06Lvpu1TEe9gnd6ZFeiBoRO9JvzEv6xK7EX97/dUE8g/vBMTqTS3CA==",
-      "license": "MIT",
       "engines": {
         "node": ">=14"
       }
@@ -16532,18 +16203,18 @@
       }
     },
     "node_modules/ibm-cloud-sdk-core/node_modules/@types/node": {
-      "version": "18.19.111",
-      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.111.tgz",
-      "integrity": "sha512-90sGdgA+QLJr1F9X79tQuEut0gEYIfkX9pydI4XGRgvFo9g2JWswefI+WUSUHPYVBHYSEfTEqBxA5hQvAZB3Mw==",
+      "version": "18.19.119",
+      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.119.tgz",
+      "integrity": "sha512-d0F6m9itIPaKnrvEMlzE48UjwZaAnFW7Jwibacw9MNdqadjKNpUm9tfJYDwmShJmgqcoqYUX3EMKO1+RWiuuNg==",
       "peer": true,
       "dependencies": {
         "undici-types": "~5.26.4"
       }
     },
     "node_modules/ibm-cloud-sdk-core/node_modules/axios": {
-      "version": "1.9.0",
-      "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz",
-      "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==",
+      "version": "1.10.0",
+      "resolved": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz",
+      "integrity": "sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==",
       "peer": true,
       "dependencies": {
         "follow-redirects": "^1.15.6",
@@ -16613,9 +16284,9 @@
       }
     },
     "node_modules/import-in-the-middle": {
-      "version": "1.14.1",
-      "resolved": "https://registry.npmjs.org/import-in-the-middle/-/import-in-the-middle-1.14.1.tgz",
-      "integrity": "sha512-FygQ6qrqLkLoT0eCKymIKvFH2bAiGDNwg0Cc6I8MevNXxhTAgwLu7it4vVwCpFjyEiJLPrjKodP9fIJAMKpIBw==",
+      "version": "1.14.2",
+      "resolved": "https://registry.npmjs.org/import-in-the-middle/-/import-in-the-middle-1.14.2.tgz",
+      "integrity": "sha512-5tCuY9BV8ujfOpwtAGgsTx9CGUapcFMEEyByLv1B+v2+6DhAcw+Zr0nhQT7uwaZ7DiourxFEscghOR8e1aPLQw==",
       "dependencies": {
         "acorn": "^8.14.0",
         "acorn-import-attributes": "^1.9.5",
@@ -18838,7 +18509,6 @@
       "version": "2.0.2",
       "resolved": "https://registry.npmjs.org/json11/-/json11-2.0.2.tgz",
       "integrity": "sha512-HIrd50UPYmP6sqLuLbFVm75g16o0oZrVfxrsY0EEys22klz8mRoWlX9KAEDOSOR9Q34rcxsyC8oDveGrCz5uLQ==",
-      "license": "MIT",
       "bin": {
         "json11": "dist/cli.mjs"
       }
@@ -19054,6 +18724,18 @@
         }
       }
     },
+    "node_modules/langchain/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
     "node_modules/langsmith": {
       "version": "0.2.15",
       "resolved": "https://registry.npmjs.org/langsmith/-/langsmith-0.2.15.tgz",
@@ -19088,6 +18770,18 @@
         "node": ">=14"
       }
     },
+    "node_modules/langsmith/node_modules/uuid": {
+      "version": "10.0.0",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
+      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "funding": [
+        "https://github.com/sponsors/broofa",
+        "https://github.com/sponsors/ctavan"
+      ],
+      "bin": {
+        "uuid": "dist/bin/uuid"
+      }
+    },
     "node_modules/leven": {
       "version": "3.1.0",
       "resolved": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz",
@@ -19111,9 +18805,9 @@
       }
     },
     "node_modules/libphonenumber-js": {
-      "version": "1.12.9",
-      "resolved": "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.12.9.tgz",
-      "integrity": "sha512-VWwAdNeJgN7jFOD+wN4qx83DTPMVPPAUyx9/TUkBXKLiNkuWWk6anV0439tgdtwaJDrEdqkvdN22iA6J4bUCZg=="
+      "version": "1.12.10",
+      "resolved": "https://registry.npmjs.org/libphonenumber-js/-/libphonenumber-js-1.12.10.tgz",
+      "integrity": "sha512-E91vHJD61jekHHR/RF/E83T/CMoaLXT7cwYA75T4gim4FZjnM6hbJjVIGg7chqlSqRsSvQ3izGmOjHy1SQzcGQ=="
     },
     "node_modules/lines-and-columns": {
       "version": "1.2.4",
@@ -19461,13 +19155,11 @@
       }
     },
     "node_modules/mariadb": {
-      "version": "3.4.2",
-      "resolved": "https://registry.npmjs.org/mariadb/-/mariadb-3.4.2.tgz",
-      "integrity": "sha512-B17vhYRHDMQ1XXvhSWsvKJbpw3Q8B6py93ThBEXZXSgxIbqnKqoHK1RzoPLbIxoEzWN3jA86ZaMMc3IG6L5wsw==",
+      "version": "3.4.4",
+      "resolved": "https://registry.npmjs.org/mariadb/-/mariadb-3.4.4.tgz",
+      "integrity": "sha512-feIbZJM8q1ddPAcG9UXcY9/afrpQMxIN3pWQTYBnwCTzQcFkZ6RJHFWB46mDHoLFdQh02lZgyg3uq6Y/74E5bA==",
       "dev": true,
       "dependencies": {
-        "@types/geojson": "^7946.0.14",
-        "@types/node": "^22.5.4",
         "denque": "^2.1.0",
         "iconv-lite": "^0.6.3",
         "lru-cache": "^10.3.0"
@@ -19476,15 +19168,6 @@
         "node": ">= 14"
       }
     },
-    "node_modules/mariadb/node_modules/@types/node": {
-      "version": "22.15.31",
-      "resolved": "https://registry.npmjs.org/@types/node/-/node-22.15.31.tgz",
-      "integrity": "sha512-jnVe5ULKl6tijxUhvQeNbQG/84fHfg+yMak02cT8QVhBx/F05rAVxCGBYYTh2EKz22D6JF5ktXuNwdx7b9iEGw==",
-      "dev": true,
-      "dependencies": {
-        "undici-types": "~6.21.0"
-      }
-    },
     "node_modules/mariadb/node_modules/iconv-lite": {
       "version": "0.6.3",
       "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz",
@@ -19503,12 +19186,6 @@
       "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==",
       "dev": true
     },
-    "node_modules/mariadb/node_modules/undici-types": {
-      "version": "6.21.0",
-      "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz",
-      "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==",
-      "dev": true
-    },
     "node_modules/math-intrinsics": {
       "version": "1.1.0",
       "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz",
@@ -19873,9 +19550,9 @@
       "dev": true
     },
     "node_modules/nan": {
-      "version": "2.22.2",
-      "resolved": "https://registry.npmjs.org/nan/-/nan-2.22.2.tgz",
-      "integrity": "sha512-DANghxFkS1plDdRsX0X9pm0Z6SJNN6gBdtXfanwoZ8hooC5gosGFSBGRYHUVPz1asKA/kMRqDRdHrluZ61SpBQ==",
+      "version": "2.23.0",
+      "resolved": "https://registry.npmjs.org/nan/-/nan-2.23.0.tgz",
+      "integrity": "sha512-1UxuyYGdoQHcGg87Lkqm3FzefucTa0NAiOcuRsDmysep3c1LVCRK2krrUDafMWtjSG04htvAmvg96+SDknOmgQ==",
       "optional": true
     },
     "node_modules/natural-compare": {
@@ -19998,17 +19675,17 @@
       }
     },
     "node_modules/newrelic/node_modules/@opentelemetry/semantic-conventions": {
-      "version": "1.34.0",
-      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.34.0.tgz",
-      "integrity": "sha512-aKcOkyrorBGlajjRdVoJWHTxfxO1vCNHLJVlSDaRHDIdjU+pX8IYQPvPDkYiujKLbRnWU+1TBwEt0QRgSm4SGA==",
+      "version": "1.36.0",
+      "resolved": "https://registry.npmjs.org/@opentelemetry/semantic-conventions/-/semantic-conventions-1.36.0.tgz",
+      "integrity": "sha512-TtxJSRD8Ohxp6bKkhrm27JRHAxPczQA7idtcTOMYI+wQRRrfgqxHv1cFbCApcSnNjtXkmzFozn6jQtFrOmbjPQ==",
       "engines": {
         "node": ">=14"
       }
     },
     "node_modules/newrelic/node_modules/agent-base": {
-      "version": "7.1.3",
-      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz",
-      "integrity": "sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==",
+      "version": "7.1.4",
+      "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.4.tgz",
+      "integrity": "sha512-MnA+YT8fwfJPgBx3m60MNqakm30XOkyIoH1y6huTQvC0PwZG7ki8NacLBcrPbNoo8vEZy7Jpuk7+jMO+CUovTQ==",
       "engines": {
         "node": ">= 14"
       }
@@ -20388,9 +20065,9 @@
       }
     },
     "node_modules/openai/node_modules/@types/node": {
-      "version": "18.19.111",
-      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.111.tgz",
-      "integrity": "sha512-90sGdgA+QLJr1F9X79tQuEut0gEYIfkX9pydI4XGRgvFo9g2JWswefI+WUSUHPYVBHYSEfTEqBxA5hQvAZB3Mw==",
+      "version": "18.19.119",
+      "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.119.tgz",
+      "integrity": "sha512-d0F6m9itIPaKnrvEMlzE48UjwZaAnFW7Jwibacw9MNdqadjKNpUm9tfJYDwmShJmgqcoqYUX3EMKO1+RWiuuNg==",
       "dependencies": {
         "undici-types": "~5.26.4"
       }
@@ -20787,9 +20464,9 @@
       }
     },
     "node_modules/pg-protocol": {
-      "version": "1.10.0",
-      "resolved": "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.10.0.tgz",
-      "integrity": "sha512-IpdytjudNuLv8nhlHs/UrVBhU0e78J0oIS/0AVdTbWxSOkFUVdsHC/NrorO6nXsQNDTT1kzDSOMJubBQviX18Q=="
+      "version": "1.10.3",
+      "resolved": "https://registry.npmjs.org/pg-protocol/-/pg-protocol-1.10.3.tgz",
+      "integrity": "sha512-6DIBgBQaTKDJyxnXaLiLR8wBpQQcGWuAESkRBX/t6OwA8YsqP+iVSiond2EDy6Y/dsGk8rh/jtax3js5NeV7JQ=="
     },
     "node_modules/pg-types": {
       "version": "2.2.0",
@@ -21174,9 +20851,9 @@
       }
     },
     "node_modules/pump": {
-      "version": "3.0.2",
-      "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz",
-      "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==",
+      "version": "3.0.3",
+      "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz",
+      "integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==",
       "optional": true,
       "dependencies": {
         "end-of-stream": "^1.1.0",
@@ -21878,8 +21555,7 @@
     "node_modules/secure-json-parse": {
       "version": "2.7.0",
       "resolved": "https://registry.npmjs.org/secure-json-parse/-/secure-json-parse-2.7.0.tgz",
-      "integrity": "sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==",
-      "license": "BSD-3-Clause"
+      "integrity": "sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw=="
     },
     "node_modules/semver": {
       "version": "7.7.2",
@@ -22174,9 +21850,9 @@
       }
     },
     "node_modules/soap/node_modules/axios": {
-      "version": "1.9.0",
-      "resolved": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz",
-      "integrity": "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==",
+      "version": "1.10.0",
+      "resolved": "https://registry.npmjs.org/axios/-/axios-1.10.0.tgz",
+      "integrity": "sha512-/1xYAC4MP/HEG+3duIhFr4ZQXR4sQXOIe+o6sdqzeykGLx6Upp/1p8MHqhINOvGeP7xyNHe7tsiJByc4SSVUxw==",
       "dependencies": {
         "follow-redirects": "^1.15.6",
         "form-data": "^4.0.0",
@@ -22597,7 +22273,7 @@
       "version": "8.1.2",
       "resolved": "https://registry.npmjs.org/superagent/-/superagent-8.1.2.tgz",
       "integrity": "sha512-6WTxW1EB6yCxV5VFOIPQruWGHqc3yI7hEmZK6h+pyk69Lk/Ut7rLUY6W/ONF2MjBuGjvmMiIpsrVJ2vjrHlslA==",
-      "deprecated": "Please upgrade to v9.0.0+ as we have fixed a public vulnerability with formidable dependency. Note that v9.0.0+ requires Node.js v14.18.0+. See https://github.com/ladjs/superagent/pull/1800 for insight. This project is supported and maintained by the team at Forward Email @ https://forwardemail.net",
+      "deprecated": "Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net",
       "dev": true,
       "dependencies": {
         "component-emitter": "^1.3.0",
@@ -22646,6 +22322,7 @@
       "version": "6.3.3",
       "resolved": "https://registry.npmjs.org/supertest/-/supertest-6.3.3.tgz",
       "integrity": "sha512-EMCG6G8gDu5qEqRQ3JjjPs6+FYT1a7Hv5ApHvtSghmOFJYtsU5S+pSb6Y2EUeCEY3CmEL3mmQ8YWlPOzQomabA==",
+      "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net",
       "dev": true,
       "dependencies": {
         "methods": "^1.1.2",
@@ -22829,9 +22506,9 @@
       "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="
     },
     "node_modules/terser": {
-      "version": "5.42.0",
-      "resolved": "https://registry.npmjs.org/terser/-/terser-5.42.0.tgz",
-      "integrity": "sha512-UYCvU9YQW2f/Vwl+P0GfhxJxbUGLwd+5QrrGgLajzWAtC/23AX0vcise32kkP7Eu0Wu9VlzzHAXkLObgjQfFlQ==",
+      "version": "5.43.1",
+      "resolved": "https://registry.npmjs.org/terser/-/terser-5.43.1.tgz",
+      "integrity": "sha512-+6erLbBm0+LROX2sPXlUYx/ux5PyE9K/a92Wrt6oA+WDAoFTdpHE5tCYCI5PNzq2y8df4rA+QgHLJuR4jNymsg==",
       "dev": true,
       "dependencies": {
         "@jridgewell/source-map": "^0.3.3",
@@ -23040,13 +22717,14 @@
       "integrity": "sha512-tktOkFUA4kXx2hhhrB8bIFb5TbwzS4uOhKEmwiD+NoiL0qtP2OQ9mFldbgD4dV1djrlBYP6eBuQZiWjuHUpqFw=="
     },
     "node_modules/then-request/node_modules/form-data": {
-      "version": "2.5.3",
-      "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.5.3.tgz",
-      "integrity": "sha512-XHIrMD0NpDrNM/Ckf7XJiBbLl57KEhT3+i3yY+eWm+cqYZJQTZrKo8Y8AWKnuV5GT4scfuUGt9LzNoIx3dU1nQ==",
+      "version": "2.5.4",
+      "resolved": "https://registry.npmjs.org/form-data/-/form-data-2.5.4.tgz",
+      "integrity": "sha512-Y/3MmRiR8Nd+0CUtrbvcKtKzLWiUfpQ7DFVggH8PwmGt/0r7RSy32GuP4hpCJlQNEBusisSx1DLtD8uD386HJQ==",
       "dependencies": {
         "asynckit": "^0.4.0",
         "combined-stream": "^1.0.8",
         "es-set-tostringtag": "^2.1.0",
+        "has-own": "^1.0.1",
         "mime-types": "^2.1.35",
         "safe-buffer": "^5.2.1"
       },
@@ -23776,9 +23454,9 @@
       }
     },
     "node_modules/uuid": {
-      "version": "10.0.0",
-      "resolved": "https://registry.npmjs.org/uuid/-/uuid-10.0.0.tgz",
-      "integrity": "sha512-8XkAphELsDnEGrDxUOHB3RGvXz6TeuYSGEZBOjtTtPm2lwhGBjLgOzLHB63IUWfBpNucQjND6d3AOudO+H3RWQ==",
+      "version": "9.0.1",
+      "resolved": "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz",
+      "integrity": "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==",
       "funding": [
         "https://github.com/sponsors/broofa",
         "https://github.com/sponsors/ctavan"
@@ -23868,22 +23546,23 @@
       "integrity": "sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ=="
     },
     "node_modules/webpack": {
-      "version": "5.99.9",
-      "resolved": "https://registry.npmjs.org/webpack/-/webpack-5.99.9.tgz",
-      "integrity": "sha512-brOPwM3JnmOa+7kd3NsmOUOwbDAj8FT9xDsG3IW0MgbN9yZV7Oi/s/+MNQ/EcSMqw7qfoRyXPoeEWT8zLVdVGg==",
+      "version": "5.100.2",
+      "resolved": "https://registry.npmjs.org/webpack/-/webpack-5.100.2.tgz",
+      "integrity": "sha512-QaNKAvGCDRh3wW1dsDjeMdDXwZm2vqq3zn6Pvq4rHOEOGSaUMgOOjG2Y9ZbIGzpfkJk9ZYTHpDqgDfeBDcnLaw==",
       "dev": true,
       "peer": true,
       "dependencies": {
         "@types/eslint-scope": "^3.7.7",
-        "@types/estree": "^1.0.6",
+        "@types/estree": "^1.0.8",
         "@types/json-schema": "^7.0.15",
         "@webassemblyjs/ast": "^1.14.1",
         "@webassemblyjs/wasm-edit": "^1.14.1",
         "@webassemblyjs/wasm-parser": "^1.14.1",
-        "acorn": "^8.14.0",
+        "acorn": "^8.15.0",
+        "acorn-import-phases": "^1.0.3",
         "browserslist": "^4.24.0",
         "chrome-trace-event": "^1.0.2",
-        "enhanced-resolve": "^5.17.1",
+        "enhanced-resolve": "^5.17.2",
         "es-module-lexer": "^1.2.1",
         "eslint-scope": "5.1.1",
         "events": "^3.2.0",
@@ -23897,7 +23576,7 @@
         "tapable": "^2.1.1",
         "terser-webpack-plugin": "^5.3.11",
         "watchpack": "^2.4.1",
-        "webpack-sources": "^3.2.3"
+        "webpack-sources": "^3.3.3"
       },
       "bin": {
         "webpack": "bin/webpack.js"
@@ -23925,9 +23604,9 @@
       }
     },
     "node_modules/webpack-sources": {
-      "version": "3.3.2",
-      "resolved": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.2.tgz",
-      "integrity": "sha512-ykKKus8lqlgXX/1WjudpIEjqsafjOTcOJqxnAbMLAu/KCsDCJ6GBtvscewvTkrn24HsnvFwrSCbenFrhtcCsAA==",
+      "version": "3.3.3",
+      "resolved": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.3.3.tgz",
+      "integrity": "sha512-yd1RBzSGanHkitROoPFd6qsrxt+oFhg/129YzheDGqeustzX0vTZJZsSsQjVQC4yzBQ56K55XU8gaNCtIzOnTg==",
       "dev": true,
       "engines": {
         "node": ">=10.13.0"
@@ -24363,9 +24042,9 @@
       }
     },
     "node_modules/ws": {
-      "version": "8.18.2",
-      "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz",
-      "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==",
+      "version": "8.18.3",
+      "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz",
+      "integrity": "sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==",
       "engines": {
         "node": ">=10.0.0"
       },
@@ -24511,9 +24190,9 @@
       }
     },
     "node_modules/zod-to-json-schema": {
-      "version": "3.24.5",
-      "resolved": "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.5.tgz",
-      "integrity": "sha512-/AuWwMP+YqiPbsJx5D6TfgRTc4kTLjsh5SOcd4bLsfUg2RcEXrFMJl1DGgdHy2aCfsIA/cr/1JM0xcB2GZji8g==",
+      "version": "3.24.6",
+      "resolved": "https://registry.npmjs.org/zod-to-json-schema/-/zod-to-json-schema-3.24.6.tgz",
+      "integrity": "sha512-h/z3PKvcTcTetyjl1fkj79MHNEjm+HpD6NXheWjzOekY7kV+lwDYnHw+ivHkijnCSMz1yJaWBD9vu/Fcmk+vEg==",
       "peerDependencies": {
         "zod": "^3.24.1"
       }
diff --git a/prisma/migrations/20250717201427_add_collect_cash_stats_table/migration.sql b/prisma/migrations/20250717201427_add_collect_cash_stats_table/migration.sql
new file mode 100644
index 00000000..1c100abb
--- /dev/null
+++ b/prisma/migrations/20250717201427_add_collect_cash_stats_table/migration.sql
@@ -0,0 +1,42 @@
+-- AlterTable
+ALTER TABLE "business_base"."portfolio" ALTER COLUMN "follow_up_after" SET DEFAULT 175,
+ALTER COLUMN "follow_up_expression" SET DEFAULT '0 11-23/3 * * *',
+ALTER COLUMN "max_follow_ups" SET DEFAULT 1;
+
+-- CreateTable
+CREATE TABLE "business_base"."collect_cash_stats" (
+    "id" UUID NOT NULL,
+    "customer_id" UUID NOT NULL,
+    "portfolio_id" UUID NOT NULL,
+    "portfolio_item_id" UUID NOT NULL,
+    "workflow_id" UUID NOT NULL,
+    "deal_value" INTEGER NOT NULL,
+    "installments" INTEGER NOT NULL,
+    "original_debt" INTEGER NOT NULL,
+    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
+    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
+    "updated_at" TIMESTAMP(3) NOT NULL,
+
+    CONSTRAINT "collect_cash_stats_pkey" PRIMARY KEY ("id")
+);
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_id_idx" ON "business_base"."collect_cash_stats"("id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_customer_id_idx" ON "business_base"."collect_cash_stats"("customer_id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_portfolio_id_idx" ON "business_base"."collect_cash_stats"("portfolio_id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_portfolio_item_id_idx" ON "business_base"."collect_cash_stats"("portfolio_item_id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_workflow_id_idx" ON "business_base"."collect_cash_stats"("workflow_id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_customer_id_portfolio_id_idx" ON "business_base"."collect_cash_stats"("customer_id", "portfolio_id");
+
+-- CreateIndex
+CREATE INDEX "collect_cash_stats_created_at_idx" ON "business_base"."collect_cash_stats"("created_at");
diff --git a/prisma/schema/business_base/collect-cash-stats.prisma b/prisma/schema/business_base/collect-cash-stats.prisma
new file mode 100644
index 00000000..3345f2fc
--- /dev/null
+++ b/prisma/schema/business_base/collect-cash-stats.prisma
@@ -0,0 +1,23 @@
+model collectCashStats {
+  id                   String   @id @default(uuid()) @db.Uuid
+  customerId           String   @map(name: "customer_id") @db.Uuid
+  portfolioId          String   @map(name: "portfolio_id") @db.Uuid
+  portfolioItemId      String   @map(name: "portfolio_item_id") @db.Uuid
+  workflowId           String   @map(name: "workflow_id") @db.Uuid
+  dealValue            Int      @map(name: "deal_value")
+  installments         Int      @map(name: "installments")
+  originalDebt         Int      @map(name: "original_debt")
+  status               String   @default("ACTIVE")
+  createdAt            DateTime @default(now()) @map(name: "created_at")
+  updatedAt            DateTime @updatedAt @map(name: "updated_at")
+
+  @@index([id])
+  @@index([customerId])
+  @@index([portfolioId])
+  @@index([portfolioItemId])
+  @@index([workflowId])
+  @@index([customerId, portfolioId])
+  @@index([createdAt])
+  @@map(name: "collect_cash_stats")
+  @@schema("business_base")
+}
diff --git a/scripts/db_seeds/data/customer-phones.yml b/scripts/db_seeds/data/customer-phones.yml
index 50d440c2..b2e0e84f 100644
--- a/scripts/db_seeds/data/customer-phones.yml
+++ b/scripts/db_seeds/data/customer-phones.yml
@@ -1,6 +1,6 @@
 customerPhones:
   - customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c
-    phoneNumber: '************'
+    phoneNumber: '************'
     apiUrl: 'http://localhost:3012'
     communicationChannel: 'WHATSAPPSELFHOSTED'
     incomingCron: '* * * * * *'
diff --git a/scripts/db_seeds/data/customer-preferences.yml b/scripts/db_seeds/data/customer-preferences.yml
index aa4f773b..2a100d56 100644
--- a/scripts/db_seeds/data/customer-preferences.yml
+++ b/scripts/db_seeds/data/customer-preferences.yml
@@ -28,4 +28,15 @@ customerPreferences:
           "venc. data parcela primeira": "VENC_PRIM_PARCELA"
           "distribuidora NOME": "NOME_DA_DISTRIBUIDORA"
           "tel": "PHONE_NUMBER"
-        additionalHeaders: [VALOR_DIVIDA_CORRIGIDO]
\ No newline at end of file
+        additionalHeaders: [VALOR_DIVIDA_CORRIGIDO]
+      statsConfig:
+        - workflowId: "c83198a4-a85c-4ad2-aed3-5a8f1cce74a9" #use a valid workflowId 
+          recoveredValueFrom:
+            source: "middleware"
+            path: "['deal-info'].VALOR_RECUPERADO"
+          originalDebt:
+            source: "middleware"
+            path: "['valor-info'].VALOR_ORIGINAL"
+          installments:
+            source: "middleware"
+            path: "['deal-info'].NUMERO_PARCELAS"
\ No newline at end of file
diff --git a/src/business-base/application/controllers/customer-preferences.controller.ts b/src/business-base/application/controllers/customer-preferences.controller.ts
index de3c36e1..64835415 100644
--- a/src/business-base/application/controllers/customer-preferences.controller.ts
+++ b/src/business-base/application/controllers/customer-preferences.controller.ts
@@ -11,7 +11,6 @@ import {
   ValidationPipe,
 } from '@nestjs/common';
 import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
-import { UpdateCustomerPreferencesDto } from '@business-base/application/dto/in/update-customer-preferences.dto';
 import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
 import { CorrelationContextService } from '@common/services/correlation-context.service';
 import { logger } from '@edutalent/commons-sdk';
@@ -33,7 +32,7 @@ import {
 @AccountRoles(AccountRole.BASIC)
 @Controller('business-base/customer-preferences')
 export class CustomerPreferencesController {
-  constructor(private readonly customerPreferencesUseCase: CustomerPreferencesUseCase) {}
+  constructor(private readonly customerPreferencesUseCase: CustomerPreferencesUseCase) { }
 
   @ApiOperation({
     summary: 'Create customer preferences',
@@ -343,7 +342,7 @@ export class CustomerPreferencesController {
     format: 'uuid',
   })
   @ApiBody({
-    type: UpdateCustomerPreferencesDto,
+    type: CustomerPreferencesDto,
     description: 'Partial customer preferences data to update',
     examples: {
       partialUpdate: {
@@ -461,7 +460,7 @@ export class CustomerPreferencesController {
   )
   async update(
     @Param('customerId') customerId: string,
-    @Body() updateCustomerPreferencesDto: UpdateCustomerPreferencesDto,
+    @Body() updateCustomerPreferencesDto: CustomerPreferencesDto,
   ): Promise<any> {
     const traceId = CorrelationContextService.getTraceId();
 
diff --git a/src/business-base/application/dto/customer-preferences.dto.ts b/src/business-base/application/dto/customer-preferences.dto.ts
index a39f9817..5b11a7e1 100644
--- a/src/business-base/application/dto/customer-preferences.dto.ts
+++ b/src/business-base/application/dto/customer-preferences.dto.ts
@@ -10,11 +10,108 @@ import {
   IsBoolean,
   IsObject,
   IsEnum,
+  IsNotEmpty,
 } from 'class-validator';
 import { Type } from 'class-transformer';
 import { ApiProperty } from '@nestjs/swagger';
 import { RecordStatus } from '@common/enums';
 
+export enum StatsDataSource {
+  CUSTOM_DATA = 'customData',
+  MIDDLEWARE = 'middleware',
+}
+
+export class StatsFieldConfigDto {
+  @ApiProperty({
+    description: 'Source of the data (customData or middleware)',
+    enum: StatsDataSource,
+    example: StatsDataSource.MIDDLEWARE,
+    required: true,
+  })
+  @IsEnum(StatsDataSource, { message: 'source must be either customData or middleware' })
+  @IsNotEmpty()
+  readonly source: StatsDataSource;
+
+  @ApiProperty({
+    description: 'Path to the field in the data source (e.g., "VALOR_RECEBIDO" for customData or "[\'deal-info\'].VALOR_RECUPERADO" for middleware)',
+    example: '[\'deal-info\'].VALOR_RECUPERADO',
+    required: true,
+  })
+  @IsString({ message: 'path must be a string' })
+  @IsNotEmpty()
+  readonly path: string;
+
+  constructor(source: StatsDataSource, path: string) {
+    this.source = source;
+    this.path = path;
+  }
+}
+
+export class StatsConfigDto {
+  @ApiProperty({
+    description: 'UUID of the workflow this configuration applies to',
+    example: 'matrix-energia-negociador-divida-v1',
+    required: true,
+  })
+  @IsString({ message: 'workflowId must be a string' })
+  @IsNotEmpty()
+  readonly workflowName: string;
+
+  @ApiProperty({
+    description: 'Configuration for recovered value extraction',
+    type: StatsFieldConfigDto,
+    required: false,
+    example: {
+      source: 'middleware',
+      path: '[\'deal-info\'].VALOR_RECUPERADO',
+    },
+  })
+  @ValidateNested({ message: 'recoveredValueFrom must be valid' })
+  @Type(() => StatsFieldConfigDto)
+  @IsOptional()
+  readonly recoveredValueFrom?: StatsFieldConfigDto;
+
+  @ApiProperty({
+    description: 'Configuration for original debt extraction',
+    type: StatsFieldConfigDto,
+    required: false,
+    example: {
+      source: 'customData',
+      path: 'VALOR_ORIGINAL',
+    },
+  })
+  @ValidateNested({ message: 'originalDebt must be valid' })
+  @Type(() => StatsFieldConfigDto)
+  @IsOptional()
+  readonly originalDebt?: StatsFieldConfigDto;
+
+  @ApiProperty({
+    description: 'Configuration for installments extraction',
+    type: StatsFieldConfigDto,
+    required: false,
+    example: {
+      source: 'customData',
+      path: 'NUMERO_PARCELAS',
+    },
+  })
+  @ValidateNested({ message: 'installments must be valid' })
+  @Type(() => StatsFieldConfigDto)
+  @IsOptional()
+  readonly installments?: StatsFieldConfigDto;
+
+  constructor(
+    workflowName: string,
+    ...dynamicFields: any[]
+  ) {
+    this.workflowName = workflowName;
+
+    // Handle additional dynamic statistical fields
+    if (dynamicFields && dynamicFields.length > 0) {
+      Object.assign(this, dynamicFields[0]);
+    }
+  }
+}
+
 export class TaxRulesDto {
   @ApiProperty({
     description: 'Penalty fee percentage (e.g., "2.69" for 2.69%)',
@@ -92,7 +189,6 @@ export class CustomImportConfigDto {
     this.additionalHeaders = additionalHeaders;
   }
 }
-
 export class PortfolioPreferencesDto {
   @ApiProperty({
     description: 'UUID of the default workflow to be used for portfolio processing',
@@ -210,6 +306,67 @@ export class PortfolioPreferencesDto {
   @IsOptional()
   readonly customImportConfig?: CustomImportConfigDto;
 
+  @ApiProperty({
+    description: 'Array of workflow-specific statistics configurations for data extraction',
+    type: [StatsConfigDto],
+    required: false,
+    isArray: true,
+    examples: {
+      multipleWorkflows: {
+        summary: 'Multiple workflow configurations with dynamic statistical fields',
+        value: [
+          {
+            workflowId: 'matrix-energia-negociador-divida-v1',
+            recoveredValueFrom: {
+              source: 'middleware',
+              path: '[\'deal-info\'].VALOR_RECUPERADO',
+            }
+          },
+          {
+            workflowId: 'matrix-energia-negociador-divida-v2',
+            recoveredValueFrom: {
+              source: 'middleware',
+              path: '[\'deal-info\'].VALOR_RECUPERADO',
+            },
+            originalDebt: {
+              source: 'customData',
+              path: 'VALOR_ORIGINAL',
+            },
+          },
+        ],
+      },
+      dynamicFields: {
+        summary: 'Configuration with custom dynamic statistical fields',
+        value: [
+          {
+            workflowId: 'custom-workflow-id',
+            recoveredValueFrom: {
+              source: 'middleware',
+              path: '[\'deal-info\'].VALOR_RECUPERADO',
+            },
+            totalDebt: {
+              source: 'customData',
+              path: 'VALOR_TOTAL',
+            },
+            interestRate: {
+              source: 'middleware',
+              path: '[\'calculation-info\'].TAXA_JUROS',
+            },
+            paymentDate: {
+              source: 'customData',
+              path: 'DATA_PAGAMENTO',
+            },
+          },
+        ],
+      },
+    },
+  })
+  @IsArray({ message: 'statsConfig must be an array' })
+  @ValidateNested({ each: true, message: 'Each statsConfig item must be valid' })
+  @Type(() => StatsConfigDto)
+  @IsOptional()
+  readonly statsConfig?: StatsConfigDto[];
+
   constructor(
     defaultWorkflowId?: string,
     timezoneUTC?: string,
@@ -222,6 +379,7 @@ export class PortfolioPreferencesDto {
     daysBeforePaymentReminder?: number,
     paymentReminderInDay?: boolean,
     customImportConfig?: CustomImportConfigDto,
+    statsConfig?: StatsConfigDto[]
   ) {
     this.defaultWorkflowId = defaultWorkflowId;
     this.timezoneUTC = timezoneUTC;
@@ -234,9 +392,9 @@ export class PortfolioPreferencesDto {
     this.daysBeforePaymentReminder = daysBeforePaymentReminder;
     this.paymentReminderInDay = paymentReminderInDay;
     this.customImportConfig = customImportConfig;
+    this.statsConfig = statsConfig;
   }
 }
-
 export class CustomerPreferencesDto {
   @ApiProperty({
     description: 'UUID of the customer these preferences belong to',
diff --git a/src/business-base/application/dto/in/update-customer-preferences.dto.ts b/src/business-base/application/dto/in/update-customer-preferences.dto.ts
deleted file mode 100644
index 8178b0db..00000000
--- a/src/business-base/application/dto/in/update-customer-preferences.dto.ts
+++ /dev/null
@@ -1,258 +0,0 @@
-import {
-  IsUUID,
-  IsString,
-  IsArray,
-  IsNumber,
-  IsPositive,
-  ValidateNested,
-  Matches,
-  IsOptional,
-  IsObject,
-  IsEnum,
-} from 'class-validator';
-import { Type } from 'class-transformer';
-import { ApiProperty } from '@nestjs/swagger';
-import { RecordStatus } from '@common/enums';
-export class UpdateTaxRulesDto {
-  @ApiProperty({
-    description: 'Penalty fee percentage (e.g., "2.69" for 2.69%)',
-    example: '2.69',
-    required: false,
-  })
-  @IsString({ message: 'penaltyFee must be a string' })
-  @IsOptional()
-  readonly penaltyFee?: string;
-
-  @ApiProperty({
-    description: 'Daily fee percentage (e.g., "0,0003333" for 0.0003333%)',
-    example: '0,0003333',
-    required: false,
-  })
-  @IsString({ message: 'dailyFee must be a string' })
-  @IsOptional()
-  readonly dailyFee?: string;
-
-  constructor(penaltyFee?: string, dailyFee?: string) {
-    this.penaltyFee = penaltyFee;
-    this.dailyFee = dailyFee;
-  }
-}
-
-export class UpdateCustomImportConfigDto {
-  @ApiProperty({
-    description: 'Custom CSV delimiter for portfolio import files',
-    example: '|',
-    required: false,
-  })
-  @IsString({ message: 'delimiter must be a string' })
-  @IsOptional()
-  readonly delimiter?: string;
-
-  @ApiProperty({
-    description: 'Tax rules configuration for business calculations',
-    type: UpdateTaxRulesDto,
-    required: false,
-  })
-  @ValidateNested({ message: 'taxRules must be valid' })
-  @Type(() => UpdateTaxRulesDto)
-  @IsOptional()
-  readonly taxRules?: UpdateTaxRulesDto;
-
-  @ApiProperty({
-    description: 'Header mapping for CSV column transformation',
-    example: { 'valor da >>> dívida$@': 'REFERENCIA_DA_DIVIDA' },
-    required: false,
-  })
-  @IsObject({ message: 'headerMapping must be an object' })
-  @IsOptional()
-  readonly headerMapping?: Record<string, string>;
-
-  @ApiProperty({
-    description: 'Post-processing headers for CSV files',
-    example: ['status', 'lastInteraction', 'followUpCount'],
-    type: [String],
-    required: false,
-  })
-  @IsArray({ message: 'additionalHeaders must be an array' })
-  @IsString({ each: true, message: 'Each post-processing header must be a string' })
-  @IsOptional()
-  readonly additionalHeaders?: string[];
-
-  constructor(
-    delimiter?: string,
-    taxRules?: UpdateTaxRulesDto,
-    headerMapping?: Record<string, string>,
-    additionalHeaders?: string[],
-  ) {
-    this.delimiter = delimiter;
-    this.taxRules = taxRules;
-    this.headerMapping = headerMapping;
-    this.additionalHeaders = additionalHeaders;
-  }
-}
-export class UpdatePortfolioPreferencesDto {
-  @ApiProperty({
-    description: 'UUID of the default workflow to be used for portfolio processing',
-    example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
-    format: 'uuid',
-    required: false,
-  })
-  @IsUUID('4', { message: 'defaultWorkflowId must be a valid UUID' })
-  @IsOptional()
-  readonly defaultWorkflowId?: string;
-
-  @ApiProperty({
-    description: 'Timezone offset in UTC format (e.g., "-3" for UTC-3, "+5.5" for UTC+5:30)',
-    example: '-3',
-    pattern: '^[+-]?\\d+(\\.\\d+)?$',
-    required: false,
-  })
-  @IsString({ message: 'timezoneUTC must be a string' })
-  @IsOptional()
-  @Matches(/^[+-]?\d+(\.\d+)?$/, {
-    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
-  })
-  readonly timezoneUTC?: string;
-
-  @ApiProperty({
-    description:
-      'Cron expression for portfolio import scheduling (5 fields: minute hour day month weekday)',
-    example: '0 9 * * 1-5',
-    pattern:
-      '^(\\*|\\*\\/[1-9]\\d*|[0-5]?\\d|[0-5]?\\d-[0-5]?\\d|[0-5]?\\d(,[0-5]?\\d)*) (\\*|\\*\\/[1-9]\\d*|[01]?\\d|2[0-3]|[01]?\\d-[01]?\\d|2[0-3]-2[0-3]|[01]?\\d(,[01]?\\d)*|2[0-3](,2[0-3])*) (\\*|\\*\\/[1-9]\\d*|[12]?\\d|3[01]|[12]?\\d-[12]?\\d|3[01]-3[01]|[12]?\\d(,[12]?\\d)*|3[01](,3[01])*) (\\*|\\*\\/[1-9]\\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\\*|\\*\\/[1-9]\\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$',
-    required: false,
-  })
-  @IsString({ message: 'importCronExpression must be a string' })
-  @IsOptional()
-  @Matches(
-    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
-    {
-      message: 'importCronExpression must be a valid cron expression',
-    },
-  )
-  readonly importCronExpression?: string;
-
-  @ApiProperty({
-    description: 'UUID of the workflow to be used for follow-up processing',
-    example: '7f413811-4aa8-43f4-8c48-d00143dd226e',
-    format: 'uuid',
-    required: false,
-  })
-  @IsUUID('4', { message: 'followUpWorkflowId must be a valid UUID' })
-  @IsOptional()
-  readonly followUpWorkflowId?: string;
-
-  @ApiProperty({
-    description:
-      'Cron expression for follow-up scheduling (5 fields: minute hour day month weekday)',
-    example: '0 */2 * * *',
-    pattern:
-      '^(\\*|\\*\\/[1-9]\\d*|[0-5]?\\d|[0-5]?\\d-[0-5]?\\d|[0-5]?\\d(,[0-5]?\\d)*) (\\*|\\*\\/[1-9]\\d*|[01]?\\d|2[0-3]|[01]?\\d-[01]?\\d|2[0-3]-2[0-3]|[01]?\\d(,[01]?\\d)*|2[0-3](,2[0-3])*) (\\*|\\*\\/[1-9]\\d*|[12]?\\d|3[01]|[12]?\\d-[12]?\\d|3[01]-3[01]|[12]?\\d(,[12]?\\d)*|3[01](,3[01])*) (\\*|\\*\\/[1-9]\\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\\*|\\*\\/[1-9]\\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$',
-    required: false,
-  })
-  @IsString({ message: 'followUpCronExpression must be a string' })
-  @IsOptional()
-  @Matches(
-    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
-    {
-      message: 'followUpCronExpression must be a valid cron expression',
-    },
-  )
-  readonly followUpCronExpression?: string;
-
-  @ApiProperty({
-    description: 'Maximum number of follow-up attempts for each portfolio item',
-    example: 3,
-    minimum: 1,
-    required: false,
-  })
-  @IsNumber({}, { message: 'followUpQuantity must be a number' })
-  @IsPositive({ message: 'followUpQuantity must be a positive number' })
-  @IsOptional()
-  readonly followUpQuantity?: number;
-
-  @ApiProperty({
-    description: 'Interval in minutes between follow-up attempts',
-    example: 120,
-    minimum: 1,
-    required: false,
-  })
-  @IsNumber({}, { message: 'followUpIntervalMinutes must be a number' })
-  @IsPositive({ message: 'followUpIntervalMinutes must be a positive number' })
-  @IsOptional()
-  readonly followUpIntervalMinutes?: number;
-
-  @ApiProperty({
-    description: 'Array of column names to include in portfolio exports',
-    example: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
-    type: [String],
-    required: false,
-  })
-  @IsArray({ message: 'exportColumns must be an array' })
-  @IsString({ each: true, message: 'Each export column must be a string' })
-  @IsOptional()
-  readonly exportColumns?: string[];
-
-  @ApiProperty({
-    description: 'Custom CSV delimiter for portfolio import files (default: ",")',
-    example: '|',
-    required: false,
-  })
-  @IsString({ message: 'delimiter must be a string' })
-  @IsOptional()
-  readonly delimiter?: string;
-
-  @ApiProperty({
-    description: 'Custom import configuration with YAML-based structure',
-    type: UpdateCustomImportConfigDto,
-    required: false,
-  })
-  @ValidateNested({ message: 'customImportConfig must be valid' })
-  @Type(() => UpdateCustomImportConfigDto)
-  @IsOptional()
-  readonly customImportConfig?: UpdateCustomImportConfigDto;
-
-  constructor(
-    defaultWorkflowId?: string,
-    timezoneUTC?: string,
-    importCronExpression?: string,
-    followUpWorkflowId?: string,
-    followUpCronExpression?: string,
-    followUpQuantity?: number,
-    followUpIntervalMinutes?: number,
-    exportColumns?: string[],
-    customImportConfig?: UpdateCustomImportConfigDto,
-  ) {
-    this.defaultWorkflowId = defaultWorkflowId;
-    this.timezoneUTC = timezoneUTC;
-    this.importCronExpression = importCronExpression;
-    this.followUpWorkflowId = followUpWorkflowId;
-    this.followUpCronExpression = followUpCronExpression;
-    this.followUpQuantity = followUpQuantity;
-    this.followUpIntervalMinutes = followUpIntervalMinutes;
-    this.exportColumns = exportColumns;
-    this.customImportConfig = customImportConfig;
-  }
-}
-
-export class UpdateCustomerPreferencesDto {
-  @ApiProperty({
-    description: 'Portfolio-specific preferences and configurations to update',
-    type: UpdatePortfolioPreferencesDto,
-    required: false,
-  })
-  @ValidateNested({ message: 'portfolio preferences must be valid' })
-  @Type(() => UpdatePortfolioPreferencesDto)
-  @IsOptional()
-  readonly portfolio?: UpdatePortfolioPreferencesDto;
-
-  @IsEnum(RecordStatus, { message: 'status must be a valid RecordStatus' })
-  @IsOptional()
-  readonly status?: RecordStatus;
-
-  constructor(data?: Partial<UpdateCustomerPreferencesDto>) {
-    if (data) {
-      Object.assign(this, data);
-    }
-  }
-}
diff --git a/src/business-base/application/use-cases/customer-preferences.use-case.ts b/src/business-base/application/use-cases/customer-preferences.use-case.ts
index 20d35c5e..b6f1f32d 100644
--- a/src/business-base/application/use-cases/customer-preferences.use-case.ts
+++ b/src/business-base/application/use-cases/customer-preferences.use-case.ts
@@ -1,6 +1,5 @@
 import { Inject, Injectable } from '@nestjs/common';
 import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
-import { UpdateCustomerPreferencesDto } from '@business-base/application/dto/in/update-customer-preferences.dto';
 import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
 import { logger } from '@edutalent/commons-sdk';
 import {
@@ -40,7 +39,7 @@ export class CustomerPreferencesUseCase {
   constructor(
     @Inject('CustomerPreferencesPort')
     private readonly customerPreferencesAdapter: CustomerPreferencesPort,
-  ) {}
+  ) { }
 
   async create(
     customerId: string,
@@ -134,7 +133,7 @@ export class CustomerPreferencesUseCase {
 
   async update(
     customerId: string,
-    updateCustomerPreferencesDto: UpdateCustomerPreferencesDto,
+    updateCustomerPreferencesDto: CustomerPreferencesDto,
   ): Promise<CustomerPreferencesDto> {
     const traceId = CorrelationContextService.getTraceId();
 
diff --git a/src/business-base/application/use-cases/portfolio-item.use-case.ts b/src/business-base/application/use-cases/portfolio-item.use-case.ts
index 632648a9..b7d589e2 100644
--- a/src/business-base/application/use-cases/portfolio-item.use-case.ts
+++ b/src/business-base/application/use-cases/portfolio-item.use-case.ts
@@ -40,8 +40,9 @@ import { MessageHubOutgoingMessagePort } from '@business-base/infrastructure/por
 import { PortfolioItemScheduledFollowUpPort } from '@business-base/infrastructure/ports/db/portfolio-item-scheduled-follow-up.port';
 import { PortfolioItemScheduledFollowUpEntity } from '@business-base/domain/entities/portfolio-item-scheduled-follow-up.entity';
 import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
-import { sanitizeMessageBody } from '@common/utils/message-sanitization.util';
 import { CorrelationContextService } from '@common/services/correlation-context.service';
+import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
+import { StatisticalDataUseCase } from './statistical-data.use-case';
 
 @Injectable()
 export class PortfolioItemUseCase {
@@ -72,6 +73,9 @@ export class PortfolioItemUseCase {
     private readonly sqsService: SQSService,
     private readonly s3Service: S3Service,
     private readonly customerPreferencesUseCase: CustomerPreferencesUseCase,
+    @Inject('CustomerPreferencesPort')
+    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
+    private readonly statisticalDataUseCase: StatisticalDataUseCase,
   ) {
     this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
   }
@@ -155,9 +159,9 @@ export class PortfolioItemUseCase {
 
     const portfolioItemMiddlewareResponseOutput = portfolioItem.middlewareResponseOutputId
       ? await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
-          portfolioItem.middlewareResponseOutputId,
-          portfolioItem.id,
-        )
+        portfolioItem.middlewareResponseOutputId,
+        portfolioItem.id,
+      )
       : null;
 
     const filteredMiddlewareResponseOutput = portfolioItemMiddlewareResponseOutput
@@ -243,6 +247,11 @@ export class PortfolioItemUseCase {
 
     await this.updateWaitingBusinessUserResponse(portfolioItemId, false);
 
+    // Automatically extract and store recovered value when status changes to SUCCEED
+    if (currentStatus === PortfolioItemStatus.SUCCEED) {
+      await this.handleStatisticValuesExtraction(updatedPortfolioItem);
+    }
+
     return this.createResponsePortfolioItemDto(updatedPortfolioItem);
   }
 
@@ -590,8 +599,7 @@ export class PortfolioItemUseCase {
     fileUrl: string | void,
   ): Promise<void> {
     logger.info(
-      `Executing item for inbound message: ${JSON.stringify(portfolioItemDto)}. Customer id: ${
-        customer.id
+      `Executing item for inbound message: ${JSON.stringify(portfolioItemDto)}. Customer id: ${customer.id
       }`,
     );
 
@@ -1179,7 +1187,7 @@ export class PortfolioItemUseCase {
       Object.entries(data)
         .filter(([_, value]) => (value as { showOff?: boolean }).showOff === true)
         .map(([key, value]) => {
-          const { showOff: _showOff, ...rest } = value as { showOff?: boolean; [key: string]: any };
+          const { showOff: _showOff, ...rest } = value as { showOff?: boolean;[key: string]: any };
           return [key, rest];
         }),
     );
@@ -1199,32 +1207,63 @@ export class PortfolioItemUseCase {
     }
   }
 
-  async getCustomerIdByPortfolioItemId(portfolioItemId: string): Promise<string> {
-    logger.info(`Getting customerId for portfolioItemId: ${portfolioItemId}`);
-
-    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
+  private async handleStatisticValuesExtraction(portfolioItem: PortfolioItemEntity): Promise<void> {
+    const traceId = CorrelationContextService.getTraceId();
+    try {
+      logger.info('Handling automatic statistical data extraction for SUCCEED status', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        portfolioId: portfolioItem.portfolioId,
+        operation: 'handleStatisticValuesExtraction',
+        layer: 'USE_CASE',
+      });
 
-    if (!portfolioItem) {
-      logger.error(`Portfolio item not found: ${portfolioItemId}`);
-      throw new BusinessException(
-        'Portfolio-item-use-case',
-        `PortfolioItem with id ${portfolioItemId} not found`,
-        BusinessExceptionStatus.ITEM_NOT_FOUND,
-      );
-    }
+      const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
 
-    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
+      // Get customer preferences to determine data source
+      const customerPreferences = await this.customerPreferencesAdapter.getById(portfolio.customerId);
+
+      const statsConfig = customerPreferences?.portfolio?.statsConfig;
+      if (!statsConfig || statsConfig.length === 0) {
+        logger.info('No stats config found for customer', {
+          traceId,
+          customerId: portfolio.customerId,
+          portfolioItemId: portfolioItem.id,
+          operation: 'handleStatisticValuesExtraction',
+          layer: 'USE_CASE',
+        });
+        return;
+      }
 
-    if (!portfolio) {
-      logger.error(`Portfolio not found for portfolioItem: ${portfolioItemId}`);
-      throw new BusinessException(
-        'Portfolio-item-use-case',
-        `Portfolio not found for portfolioItem ${portfolioItemId}`,
-        BusinessExceptionStatus.ITEM_NOT_FOUND,
+      // Call the statistical data use case to extract and store all statistical data
+      await this.statisticalDataUseCase.extractAndStoreStatisticalData(
+        portfolio.customerId,
+        portfolioItem.portfolioId,
+        portfolioItem.id,
+        portfolio.workflowId,
+        statsConfig,
       );
-    }
 
-    return portfolio.customerId;
+      logger.info('Automatic statistical data extraction completed successfully', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        portfolioId: portfolioItem.portfolioId,
+        customerId: portfolio.customerId,
+        workflowId: portfolio.workflowId,
+        operation: 'handleStatisticValuesExtraction',
+        layer: 'USE_CASE',
+      });
+    } catch (error) {
+      logger.error('Error during automatic statistical data extraction', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        portfolioId: portfolioItem.portfolioId,
+        error: JSON.stringify(error),
+        operation: 'handleStatisticValuesExtraction',
+        layer: 'USE_CASE',
+        message: 'Statistical data extraction failed but status update will continue',
+      });
+    }
   }
 
   private calculateInstallmentDates(
diff --git a/src/business-base/application/use-cases/statistical-data.use-case.ts b/src/business-base/application/use-cases/statistical-data.use-case.ts
new file mode 100644
index 00000000..bf29e946
--- /dev/null
+++ b/src/business-base/application/use-cases/statistical-data.use-case.ts
@@ -0,0 +1,386 @@
+import { Inject, Injectable } from '@nestjs/common';
+import { logger } from '@edutalent/commons-sdk';
+import { BusinessException } from '@common/exception/types/BusinessException';
+import { CorrelationContextService } from '@common/services/correlation-context.service';
+import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
+import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
+import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
+import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
+import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
+import { RecordStatus } from '@common/enums';
+import { StatsConfigDto, StatsDataSource, StatsFieldConfigDto } from '../dto/customer-preferences.dto';
+import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
+import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
+
+export interface RecoveredValueSummary {
+  portfolioId: string;
+  portfolioName: string;
+  totalRecoveredValue: number;
+  itemCount: number;
+}
+
+export interface CustomerRecoveredValueSummary {
+  customerId: string;
+  totalRecoveredValue: number;
+  portfolios: RecoveredValueSummary[];
+}
+
+@Injectable()
+export class StatisticalDataUseCase {
+  constructor(
+    @Inject('CollectCashStatsPort')
+    private readonly collectCashStatsAdapter: CollectCashStatsPort,
+    @Inject('PortfolioItemPort')
+    private readonly portfolioItemAdapter: PortfolioItemPort,
+    @Inject('PortfolioItemCustomDataPort')
+    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
+    @Inject('MiddlewareResponseOutputPort')
+    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
+    @Inject('InfraWorkflowPort')
+    private readonly workflowAdapter: InfraWorkflowPort,
+  ) { }
+
+  /**
+   * Extracts and stores statistical data for a portfolio item based on workflow configuration
+   */
+  async extractAndStoreStatisticalData(
+    customerId: string,
+    portfolioId: string,
+    portfolioItemId: string,
+    workflowId: string,
+    statsConfig: StatsConfigDto[],
+  ): Promise<void> {
+    const traceId = CorrelationContextService.getTraceId();
+
+    try {
+      logger.info('Extracting and storing statistical data', {
+        traceId,
+        customerId,
+        portfolioId,
+        portfolioItemId,
+        workflowId,
+        operation: 'extractAndStoreStatisticalData',
+        layer: 'USE_CASE',
+      });
+
+      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
+
+      // Find workflow-specific configuration
+      const workflowConfig = await this.findWorkflowConfig(statsConfig);
+      if (!workflowConfig) {
+        logger.info('No workflow-specific statistical configuration found', {
+          traceId,
+          workflowId,
+          portfolioItemId,
+          operation: 'extractAndStoreStatisticalData',
+          layer: 'USE_CASE',
+        });
+        return;
+      }
+
+      for (const config of workflowConfig) {
+        if (config.workflowId === workflowId) {
+          // Extract statistical values
+          const extractedData = await this.extractStatisticalValues(config, portfolioItem);
+
+          // Only store if we have valid data (any value > 0 cents)
+          if (extractedData.dealValueCents > 0 || extractedData.installments > 0 || extractedData.originalDebtCents > 0) {
+            const collectCashStats = new CollectCashStatsEntity(
+              customerId,
+              portfolioId,
+              portfolioItemId,
+              workflowId,
+              extractedData.dealValueCents,
+              extractedData.originalDebtCents,
+              extractedData.installments,
+              RecordStatus.ACTIVE,
+            );
+
+            await this.collectCashStatsAdapter.create(collectCashStats);
+
+            logger.info('Statistical data stored successfully', {
+              traceId,
+              customerId,
+              portfolioId,
+              portfolioItemId,
+              workflowId,
+              dealValueCents: extractedData.dealValueCents,
+              originalDebtCents: extractedData.originalDebtCents,
+              dealValueReais: (extractedData.dealValueCents / 100).toFixed(2),
+              originalDebtReais: (extractedData.originalDebtCents / 100).toFixed(2),
+              operation: 'extractAndStoreStatisticalData',
+              layer: 'USE_CASE',
+            });
+          } else {
+            logger.info('No valid statistical data to store (all values are 0 cents)', {
+              traceId,
+              portfolioItemId,
+              workflowId,
+              dealValueCents: extractedData.dealValueCents,
+              originalDebtCents: extractedData.originalDebtCents,
+              operation: 'extractAndStoreStatisticalData',
+              layer: 'USE_CASE',
+            });
+          }
+        }
+      }
+    } catch (error) {
+      logger.error('Error extracting and storing statistical data', {
+        traceId,
+        customerId,
+        portfolioId,
+        portfolioItemId,
+        workflowId,
+        error: JSON.stringify(error),
+        operation: 'extractAndStoreStatisticalData',
+        layer: 'USE_CASE',
+      });
+      throw new BusinessException(
+        'StatisticalDataUseCase',
+        'extractAndStoreStatisticalData',
+        error,
+      );
+    }
+  }
+
+  /**
+   * Finds the workflow-specific configuration from statsConfig array
+   */
+  private async findWorkflowConfig(statsConfig: StatsConfigDto[]): Promise<(StatsConfigDto & { workflowId: string })[] | null> {
+    const traceId = CorrelationContextService.getTraceId();
+    if (!statsConfig || statsConfig.length === 0) {
+      return null;
+    }
+
+    try {
+      const workflowConfigs: (StatsConfigDto & { workflowId: string })[] = [];
+      for (const config of statsConfig) {
+        const workflow = await this.workflowAdapter.getWorkflowByName(config.workflowName);
+        workflowConfigs.push({
+          ...config,
+          workflowId: workflow.workflowId,
+        });
+      }
+      return workflowConfigs;
+    } catch (error) {
+      logger.error('Error finding workflow by ID, trying by name', {
+        traceId,
+        error: JSON.stringify(error),
+        operation: 'findWorkflowConfig',
+        layer: 'USE_CASE',
+      });
+    }
+  }
+
+  /**
+   * Extracts statistical values from a workflow configuration
+   * @returns Object with monetary values in integer cents for financial precision
+   */
+  private async extractStatisticalValues(
+    workflowConfig: StatsConfigDto,
+    portfolioItem: PortfolioItemEntity,
+  ): Promise<{
+    dealValueCents: number;
+    originalDebtCents: number;
+    installments: number;
+  }> {
+    const traceId = CorrelationContextService.getTraceId();
+    const result = { dealValueCents: 0, originalDebtCents: 0, installments: 0 };
+
+    try {
+      // Extract dealValue from recoveredValueFrom (in cents)
+      if (workflowConfig.recoveredValueFrom) {
+        result.dealValueCents = await this.extractFieldValue(
+          portfolioItem,
+          workflowConfig.recoveredValueFrom,
+          'recoveredValueFrom',
+        );
+      }
+
+      // Extract originalDebt (in cents)
+      if (workflowConfig.originalDebt) {
+        result.originalDebtCents = await this.extractFieldValue(
+          portfolioItem,
+          workflowConfig.originalDebt,
+          'originalDebt',
+        );
+      }
+
+      // Extract installments
+      if (workflowConfig.installments) {
+        result.installments = await this.extractFieldValue(
+          portfolioItem,
+          workflowConfig.installments,
+          'installments',
+        );
+      }
+
+      logger.info('Statistical values extracted successfully', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        dealValueCents: result.dealValueCents,
+        originalDebtCents: result.originalDebtCents,
+        dealValueReais: (result.dealValueCents / 100).toFixed(2),
+        originalDebtReais: (result.originalDebtCents / 100).toFixed(2),
+        installments: result.installments,
+        operation: 'extractStatisticalValues',
+        layer: 'USE_CASE',
+      });
+
+      return result;
+    } catch (error) {
+      logger.error('Error extracting statistical values', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        error: JSON.stringify(error),
+        operation: 'extractStatisticalValues',
+        layer: 'USE_CASE',
+      });
+      return result; // Return zeros on error
+    }
+  }
+
+  /**
+   * Extracts a single field value from the specified data source
+   */
+  private async extractFieldValue(
+    portfolioItem: PortfolioItemEntity,
+    fieldConfig: StatsFieldConfigDto,
+    fieldName: string,
+  ): Promise<number> {
+    const traceId = CorrelationContextService.getTraceId();
+
+    try {
+      let data: any;
+
+
+      if (fieldConfig.source === StatsDataSource.CUSTOM_DATA) {
+        const customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
+          portfolioItem.customDataId,
+          portfolioItem.id,
+        );
+        data = customData?.customData;
+      } else if (fieldConfig.source === StatsDataSource.MIDDLEWARE) {
+        if (!portfolioItem.middlewareResponseOutputId) {
+          logger.info('No middleware response found for portfolio item', {
+            traceId,
+            portfolioItemId: portfolioItem.id,
+            fieldName,
+            operation: 'extractFieldValue',
+            layer: 'USE_CASE',
+          });
+          return 0;
+        }
+
+        const middlewareResponse = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
+          portfolioItem.middlewareResponseOutputId,
+          portfolioItem.id,
+        );
+        data = middlewareResponse?.data;
+      }
+
+      if (!data) {
+        logger.info('No data found for field extraction', {
+          traceId,
+          portfolioItemId: portfolioItem.id,
+          fieldName,
+          source: fieldConfig.source,
+          operation: 'extractFieldValue',
+          layer: 'USE_CASE',
+        });
+        return 0;
+      }
+
+      // Extract value using the path
+      const rawValue = this.getValueFromPath(data, fieldConfig.path);
+      if (fieldName === 'installments') {
+        return parseInt(rawValue);
+      }
+
+      const valueInCents = this.parseNumericValueToCents(rawValue);
+
+      logger.info('Field value extracted successfully', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        fieldName,
+        source: fieldConfig.source,
+        path: fieldConfig.path,
+        rawValue,
+        valueInCents,
+        valueInReais: (valueInCents / 100).toFixed(2),
+        operation: 'extractFieldValue',
+        layer: 'USE_CASE',
+      });
+
+      return valueInCents;
+    } catch (error) {
+      logger.error('Error extracting field value', {
+        traceId,
+        portfolioItemId: portfolioItem.id,
+        fieldName,
+        source: fieldConfig.source,
+        path: fieldConfig.path,
+        error: JSON.stringify(error),
+        operation: 'extractFieldValue',
+        layer: 'USE_CASE',
+      });
+      return 0;
+    }
+  }
+
+  /**
+   * Extracts value from nested object using dot notation or bracket notation path
+   */
+  private getValueFromPath(data: any, path: string): any {
+    try {
+      // Handle bracket notation like ['deal-info'].VALOR_RECUPERADO
+      if (path.includes('[') && path.includes(']')) {
+        return Function(`"use strict"; return (${JSON.stringify(data)})${path}`)();
+      }
+
+      // Handle simple dot notation
+      return path.split('.').reduce((obj, key) => obj?.[key], data);
+    } catch (error) {
+      logger.warn('Error parsing path, returning undefined', {
+        path,
+        error: JSON.stringify(error),
+        operation: 'getValueFromPath',
+        layer: 'USE_CASE',
+      });
+      return undefined;
+    }
+  }
+
+  /**
+   * Parses a value to integer cents format for financial precision, handling Brazilian currency format
+   * Converts floating-point monetary values to integer cents to avoid precision errors
+   * @param value - The value to parse (number, string, or other)
+   * @returns Integer value in cents (e.g., 12.34 becomes 1234 cents)
+   */
+  private parseNumericValueToCents(value: any): number {
+    if (value == null || value === '') {
+      return 0;
+    }
+
+    let numericValue: number;
+
+    if (typeof value === 'number') {
+      numericValue = value;
+    } else if (typeof value === 'string') {
+      // Handle Brazilian currency format (e.g., "2.750,50" or "2,750.50")
+      const cleanValue = value.replace(',', '.'); // Replace decimal comma with dot
+
+      numericValue = parseFloat(cleanValue);
+      if (isNaN(numericValue)) {
+        return 0;
+      }
+    } else {
+      return 0;
+    }
+
+    // Convert to integer cents using Math.round for proper rounding
+    // This follows the same pattern as portfolio.use-case.ts financial calculations
+    return Math.round(numericValue * 100);
+  }
+
+}
diff --git a/src/business-base/business-base.module.ts b/src/business-base/business-base.module.ts
index a6f52759..156e7044 100644
--- a/src/business-base/business-base.module.ts
+++ b/src/business-base/business-base.module.ts
@@ -48,6 +48,8 @@ import { PortfolioItemScheduledFollowUpAdapter } from '@business-base/infrastruc
 import { CustomerPreferencesController } from '@business-base/application/controllers/customer-preferences.controller';
 import { CustomerPreferencesAdapter } from '@business-base/infrastructure/adapters/db/customer-preferences.adapter';
 import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
+import { CollectCashStatsAdapter } from '@business-base/infrastructure/adapters/db/collect-cash-stats.adapter';
+import { StatisticalDataUseCase } from './application/use-cases/statistical-data.use-case';
 
 const httpModule = HttpModule.registerAsync({
   useFactory: () => ({
@@ -135,7 +137,12 @@ const httpModule = HttpModule.registerAsync({
       provide: 'CustomerPreferencesPort',
       useClass: CustomerPreferencesAdapter,
     },
+    {
+      provide: 'CollectCashStatsPort',
+      useClass: CollectCashStatsAdapter,
+    },
     CustomerPreferencesUseCase,
+    StatisticalDataUseCase
   ],
   controllers: [
     CustomerController,
@@ -147,4 +154,4 @@ const httpModule = HttpModule.registerAsync({
     CustomerPreferencesController,
   ],
 })
-export class BusinessBaseModule {}
+export class BusinessBaseModule { }
diff --git a/src/business-base/domain/entities/collect-cash-stats.entity.ts b/src/business-base/domain/entities/collect-cash-stats.entity.ts
new file mode 100644
index 00000000..993bac51
--- /dev/null
+++ b/src/business-base/domain/entities/collect-cash-stats.entity.ts
@@ -0,0 +1,78 @@
+import { RecordStatus } from '@common/enums';
+import {
+  IsDate,
+  IsEnum,
+  IsInt,
+  IsNotEmpty,
+  IsPositive,
+  IsUUID,
+} from 'class-validator';
+
+export class CollectCashStatsEntity {
+  @IsUUID('4')
+  @IsNotEmpty()
+  readonly customerId: string;
+
+  @IsUUID('4')
+  @IsNotEmpty()
+  readonly portfolioId: string;
+
+  @IsUUID('4')
+  @IsNotEmpty()
+  readonly portfolioItemId: string;
+
+  @IsUUID('4')
+  @IsNotEmpty()
+  readonly workflowId: string;
+
+  @IsInt()
+  @IsPositive()
+  @IsNotEmpty()
+  readonly dealValue: number;
+
+  @IsInt()
+  @IsPositive()
+  @IsNotEmpty()
+  readonly originalDebt: number;
+
+  @IsInt()
+  @IsPositive()
+  @IsNotEmpty()
+  readonly installments: number;
+
+  @IsEnum(RecordStatus)
+  @IsNotEmpty()
+  status: RecordStatus = RecordStatus.ACTIVE;
+
+  @IsDate()
+  @IsNotEmpty()
+  readonly createdAt?: Date;
+
+  @IsDate()
+  @IsNotEmpty()
+  readonly updatedAt?: Date;
+
+  constructor(
+    customerId: string,
+    portfolioId: string,
+    portfolioItemId: string,
+    workflowId: string,
+    dealValue: number,
+    originalDebt: number,
+    installments: number,
+    status: RecordStatus = RecordStatus.ACTIVE,
+    createdAt?: Date,
+    updatedAt?: Date,
+  ) {
+    this.customerId = customerId;
+    this.portfolioId = portfolioId;
+    this.portfolioItemId = portfolioItemId;
+    this.workflowId = workflowId;
+    this.dealValue = dealValue;
+    this.originalDebt = originalDebt;
+    this.installments = installments;
+    this.status = status;
+    this.createdAt = createdAt;
+    this.updatedAt = updatedAt;
+  }
+}
diff --git a/src/business-base/domain/entities/customer-preferences.entity.ts b/src/business-base/domain/entities/customer-preferences.entity.ts
index 85182391..88349b4e 100644
--- a/src/business-base/domain/entities/customer-preferences.entity.ts
+++ b/src/business-base/domain/entities/customer-preferences.entity.ts
@@ -14,6 +14,7 @@ import {
 } from 'class-validator';
 import { Type } from 'class-transformer';
 import { RecordStatus } from '@common/enums';
+import { StatsConfigDto } from '../../application/dto/customer-preferences.dto';
 
 export class TaxRules {
   @IsString()
@@ -90,12 +91,6 @@ export class PortfolioPreferences {
   timezoneUTC?: string;
 
   @IsString()
-  @Matches(
-    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
-    {
-      message: 'importCronExpression must be a valid cron expression',
-    },
-  )
   @IsOptional()
   importCronExpression?: string;
 
@@ -104,12 +99,6 @@ export class PortfolioPreferences {
   followUpWorkflowId?: string;
 
   @IsString()
-  @Matches(
-    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
-    {
-      message: 'followUpCronExpression must be a valid cron expression',
-    },
-  )
   @IsOptional()
   followUpCronExpression?: string;
 
@@ -133,6 +122,12 @@ export class PortfolioPreferences {
   @IsOptional()
   customImportConfig?: CustomImportConfig;
 
+  @IsArray()
+  @ValidateNested({ each: true })
+  @Type(() => StatsConfigDto)
+  @IsOptional()
+  statsConfig?: StatsConfigDto[];
+
   constructor(data?: Partial<PortfolioPreferences>) {
     if (data) {
       Object.assign(this, data);
diff --git a/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts b/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts
new file mode 100644
index 00000000..b4ec7da3
--- /dev/null
+++ b/src/business-base/infrastructure/adapters/db/collect-cash-stats.adapter.ts
@@ -0,0 +1,190 @@
+import { Injectable } from '@nestjs/common';
+import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
+import { PrismaService } from '@common/prisma/prisma.service';
+import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
+import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
+import { RecordStatus } from '@common/enums';
+
+@Injectable()
+export class CollectCashStatsAdapter
+  extends PrismaCommonAdapter<CollectCashStatsEntity>
+  implements CollectCashStatsPort {
+  constructor(private readonly prisma: PrismaService) {
+    super(prisma, 'collectCashStats');
+  }
+
+  async findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]> {
+    const stats = await this.prisma.client.collectCashStats.findMany({
+      where: {
+        customerId,
+        status: RecordStatus.ACTIVE,
+      },
+      orderBy: {
+        createdAt: 'desc',
+      },
+    });
+
+    return stats.map(stat => this.mapToEntity(stat));
+  }
+
+  async findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]> {
+    const stats = await this.prisma.client.collectCashStats.findMany({
+      where: {
+        portfolioId,
+        status: RecordStatus.ACTIVE,
+      },
+      orderBy: {
+        createdAt: 'desc',
+      },
+    });
+
+    return stats.map(stat => this.mapToEntity(stat));
+  }
+
+  async findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]> {
+    const stats = await this.prisma.client.collectCashStats.findMany({
+      where: {
+        portfolioItemId,
+        status: RecordStatus.ACTIVE,
+      },
+      orderBy: {
+        createdAt: 'desc',
+      },
+    });
+
+    return stats.map(stat => this.mapToEntity(stat));
+  }
+
+  async findByCustomerIdWithDateRange(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<CollectCashStatsEntity[]> {
+    const whereClause: any = {
+      customerId,
+      status: RecordStatus.ACTIVE,
+    };
+
+    if (startDate || endDate) {
+      whereClause.createdAt = {};
+      if (startDate) {
+        whereClause.createdAt.gte = startDate;
+      }
+      if (endDate) {
+        whereClause.createdAt.lte = endDate;
+      }
+    }
+
+    const stats = await this.prisma.client.collectCashStats.findMany({
+      where: whereClause,
+      orderBy: {
+        createdAt: 'desc',
+      },
+    });
+
+    return stats.map(stat => this.mapToEntity(stat));
+  }
+
+  async findByPortfolioIdWithDateRange(
+    portfolioId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<CollectCashStatsEntity[]> {
+    const whereClause: any = {
+      portfolioId,
+      status: RecordStatus.ACTIVE,
+    };
+
+    if (startDate || endDate) {
+      whereClause.createdAt = {};
+      if (startDate) {
+        whereClause.createdAt.gte = startDate;
+      }
+      if (endDate) {
+        whereClause.createdAt.lte = endDate;
+      }
+    }
+
+    const stats = await this.prisma.client.collectCashStats.findMany({
+      where: whereClause,
+      orderBy: {
+        createdAt: 'desc',
+      },
+    });
+
+    return stats.map(stat => this.mapToEntity(stat));
+  }
+
+  async getTotalRecoveredValueByCustomerId(customerId: string): Promise<number> {
+    const result = await this.prisma.client.collectCashStats.aggregate({
+      where: {
+        customerId,
+        status: RecordStatus.ACTIVE,
+      },
+      _sum: {
+        dealValue: true,
+      },
+    });
+
+    return Number(result._sum.dealValue) || 0;
+  }
+
+  async getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number> {
+    const result = await this.prisma.client.collectCashStats.aggregate({
+      where: {
+        portfolioId,
+        status: RecordStatus.ACTIVE,
+      },
+      _sum: {
+        dealValue: true,
+      },
+    });
+
+    return Number(result._sum.dealValue) || 0;
+  }
+
+  async getTotalRecoveredValueByCustomerIdWithDateRange(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<number> {
+    const whereClause: any = {
+      customerId,
+      status: RecordStatus.ACTIVE,
+    };
+
+    if (startDate || endDate) {
+      whereClause.createdAt = {};
+      if (startDate) {
+        whereClause.createdAt.gte = startDate;
+      }
+      if (endDate) {
+        whereClause.createdAt.lte = endDate;
+      }
+    }
+
+    const result = await this.prisma.client.collectCashStats.aggregate({
+      where: whereClause,
+      _sum: {
+        dealValue: true,
+      },
+    });
+
+    return Number(result._sum.dealValue) || 0;
+  }
+
+  private mapToEntity(stat: any): CollectCashStatsEntity {
+    return new CollectCashStatsEntity(
+      stat.customerId,
+      stat.portfolioId,
+      stat.portfolioItemId,
+      stat.workflowId,
+      Number(stat.dealValue), // Already in cents from database
+      Number(stat.originalDebt), // Already in cents from database
+      stat.installments,
+      stat.status as RecordStatus,
+      stat.createdAt,
+      stat.updatedAt,
+    );
+  }
+}
diff --git a/src/business-base/infrastructure/adapters/http/workflow.adapter.ts b/src/business-base/infrastructure/adapters/http/workflow.adapter.ts
index 0be720d2..a5e17fd1 100644
--- a/src/business-base/infrastructure/adapters/http/workflow.adapter.ts
+++ b/src/business-base/infrastructure/adapters/http/workflow.adapter.ts
@@ -7,6 +7,7 @@ import { ExecuteWorkflow } from '@business-base/misc/interfaces/in/execute-workf
 import { handleHttpError } from '@common/utils/handle-http-error';
 import { WorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/workflow-response.dto';
 import { ExecuteWorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/execute-workflow-response.dto';
+import { CorrelationContextService } from '@common/services/correlation-context.service';
 
 @Injectable()
 export class InfraWorkflowAdapter implements InfraWorkflowPort {
@@ -40,10 +41,10 @@ export class InfraWorkflowAdapter implements InfraWorkflowPort {
   }
 
   async executeWorkflow(executeWorkflowData: ExecuteWorkflow): Promise<ExecuteWorkflowResponseDto> {
+    const traceId = CorrelationContextService.getTraceId();
     try {
       const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/execute`;
       logger.info(`Posting data to ${url}`);
-      //TODO: set authentication
       const headers = {
         'Content-Type': 'application/json',
       };
@@ -52,11 +53,23 @@ export class InfraWorkflowAdapter implements InfraWorkflowPort {
         this.httpService.post(url, executeWorkflowData, { headers, timeout: 180000 }),
       );
 
+      logger.info('Workflow executed successfully', {
+        traceId,
+        workflowExecutionId: executeWorkflowData.workflowExecutionId,
+        statusCode: workflowResponse.status,
+      });
+
       return workflowResponse.data.data;
     } catch (error) {
-      logger.error(
-        `Execute Workflow error:  ${executeWorkflowData} Error:${JSON.stringify(error)}`,
-      );
+      logger.error('Execute Workflow error', {
+        traceId,
+        workflowExecutionId: executeWorkflowData.workflowExecutionId,
+        errorCode: error.code,
+        errorMessage: error.message,
+        statusCode: error?.response?.status,
+        url: error?.config?.url,
+      });
+
 
       handleHttpError(error, 'infra-Workflow-adapter');
     }
@@ -78,6 +91,22 @@ export class InfraWorkflowAdapter implements InfraWorkflowPort {
     }
   }
 
+  async getWorkflowByName(workflowName: string): Promise<WorkflowResponseDto> {
+    try {
+      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/name/${workflowName}`;
+      logger.info(`Posting data to ${url}`);
+      //TODO: set authentication
+      const headers = {
+        'Content-Type': 'application/json',
+      };
+      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));
+
+      return data?.data;
+    } catch (error) {
+      handleHttpError(error, 'infra-Workflow-adapter:getWorkflowByName');
+    }
+  }
+
   async getWorkflowVariables(workflowId: string): Promise<string[]> {
     const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/${workflowId}/variables`;
     logger.info(`Posting data to ${url}`);
diff --git a/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts b/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts
new file mode 100644
index 00000000..ae4c0dcc
--- /dev/null
+++ b/src/business-base/infrastructure/ports/db/collect-cash-stats.port.ts
@@ -0,0 +1,25 @@
+import { DbCommonPort } from '@common/db/ports/common.port';
+import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
+
+export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
+  findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]>;
+  findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]>;
+  findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]>;
+  findByCustomerIdWithDateRange(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<CollectCashStatsEntity[]>;
+  findByPortfolioIdWithDateRange(
+    portfolioId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<CollectCashStatsEntity[]>;
+  getTotalRecoveredValueByCustomerId(customerId: string): Promise<number>;
+  getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number>;
+  getTotalRecoveredValueByCustomerIdWithDateRange(
+    customerId: string,
+    startDate?: Date,
+    endDate?: Date,
+  ): Promise<number>;
+}
diff --git a/src/business-base/infrastructure/ports/http/workflow.port.ts b/src/business-base/infrastructure/ports/http/workflow.port.ts
index eaed3ecb..d188459d 100644
--- a/src/business-base/infrastructure/ports/http/workflow.port.ts
+++ b/src/business-base/infrastructure/ports/http/workflow.port.ts
@@ -8,5 +8,6 @@ export interface InfraWorkflowPort {
   ): Promise<{ workflowExecutionId: string; stepExecutionId: string; stepExecutionOrder: number }>;
   executeWorkflow(executeWorkflowData: ExecuteWorkflow): Promise<ExecuteWorkflowResponseDto>;
   getWorkflowById(workflowId: string): Promise<WorkflowResponseDto>;
+  getWorkflowByName(workflowName: string): Promise<WorkflowResponseDto>;
   getWorkflowVariables(workflowId: string): Promise<string[]>;
 }
diff --git a/src/common/utils/handle-http-error.ts b/src/common/utils/handle-http-error.ts
index 4ef3e2f2..79d0fb4a 100644
--- a/src/common/utils/handle-http-error.ts
+++ b/src/common/utils/handle-http-error.ts
@@ -14,12 +14,21 @@ export const handleHttpError = (error: any, name: string): void => {
     traceId,
     source: name,
     error: error.message,
+    errorCode: error.code,
+    errorName: error.name,
     statusCode: error?.response?.status,
     statusText: error?.response?.statusText,
     url: error?.config?.url,
     method: error?.config?.method,
+    timeout: error?.config?.timeout,
     responseData: error?.response?.data,
-    requestData: error?.config?.data,
+    requestData: typeof error?.config?.data === 'string' ?
+      error.config.data.substring(0, 500) + '...' : // Truncate large payloads
+      error?.config?.data,
+    connectionInfo: {
+      host: error?.config?.url ? new URL(error.config.url).host : 'unknown',
+      port: error?.config?.url ? new URL(error.config.url).port : 'unknown',
+    },
     timestamp: new Date().toISOString(),
     layer: 'HTTP_ERROR_HANDLER',
   });
@@ -45,9 +54,31 @@ export const handleHttpError = (error: any, name: string): void => {
     }
   }
 
-  // If no response data, create a meaningful error message from available information
-  const fallbackMessage =
-    error?.message || error?.code || error?.name || 'Network or connection error';
+  // Handle specific network errors with more descriptive messages
+  let fallbackMessage = error?.message || error?.code || error?.name || 'Network or connection error';
+
+  // Provide more specific error messages for common network issues
+  if (error?.code) {
+    switch (error.code) {
+      case 'ECONNRESET':
+        fallbackMessage = 'Connection was reset by the server. This may indicate server overload, network issues, or timeout.';
+        break;
+      case 'ECONNREFUSED':
+        fallbackMessage = 'Connection refused. The target service may be down or unreachable.';
+        break;
+      case 'ETIMEDOUT':
+        fallbackMessage = 'Request timed out. The server took too long to respond.';
+        break;
+      case 'ENOTFOUND':
+        fallbackMessage = 'DNS resolution failed. The hostname could not be resolved.';
+        break;
+      case 'ECONNABORTED':
+        fallbackMessage = 'Connection was aborted. The request was cancelled or interrupted.';
+        break;
+      default:
+        fallbackMessage = `Network error: ${error.code} - ${error.message || 'Unknown network issue'}`;
+    }
+  }
 
   throw new BusinessException(name, fallbackMessage, BusinessExceptionStatus.GENERAL_ERROR);
 };
diff --git a/src/orchestrator/application/controllers/workflow.controller.ts b/src/orchestrator/application/controllers/workflow.controller.ts
index a44b8615..11d0ac68 100644
--- a/src/orchestrator/application/controllers/workflow.controller.ts
+++ b/src/orchestrator/application/controllers/workflow.controller.ts
@@ -21,7 +21,7 @@ import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
 @ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
 @Controller('orchestrator/workflows')
 export class WorkflowController {
-  constructor(private readonly workflowUseCase: WorkflowUseCase) {}
+  constructor(private readonly workflowUseCase: WorkflowUseCase) { }
 
   @Get('health')
   @Version('1')
@@ -65,6 +65,16 @@ export class WorkflowController {
     };
   }
 
+  @Get('name/:name')
+  @Version('1')
+  async getWorkflowByName(@Param('name') name: string): Promise<any> {
+    const workflowResponseDto = await this.workflowUseCase.getWorkflowByName(name);
+    return {
+      statusCode: 201,
+      data: workflowResponseDto,
+    };
+  }
+
   @ApiOperation({ summary: 'Get variables for a workflow' })
   @ApiResponse({
     status: 200,
diff --git a/src/orchestrator/application/use-cases/workflow.use-case.ts b/src/orchestrator/application/use-cases/workflow.use-case.ts
index 690cdf23..3fe8abd2 100644
--- a/src/orchestrator/application/use-cases/workflow.use-case.ts
+++ b/src/orchestrator/application/use-cases/workflow.use-case.ts
@@ -55,7 +55,7 @@ export class WorkflowUseCase {
     private readonly integrationFactory: IntegrationToolFactory,
     @Inject('AgentPort')
     private readonly agentAdapter: AgentPort,
-  ) {}
+  ) { }
 
   async saveWorkflow(saveWorkflowRequestDto: SaveWorkflowRequestDto): Promise<WorkflowResponseDto> {
     logger.info('Creating workflow: ', saveWorkflowRequestDto);
@@ -160,6 +160,45 @@ export class WorkflowUseCase {
     );
   }
 
+  async getWorkflowByName(name: string): Promise<WorkflowResponseDto> {
+    logger.info('Getting workflow by name: ', name);
+    const workflow = await this.workFlowAdapter.getByName(name);
+
+    if (!workflow) {
+      throw new BusinessException('Workflow', `Workflow with name ${name} not found`, BusinessExceptionStatus.ITEM_NOT_FOUND);
+    }
+
+    const stepsWorkflow = workflow.steps.map(step => {
+      const middlewares = step.middlewares?.map(middleware => {
+        return new MiddlewareResponseDto(
+          middleware.id,
+          middleware.name,
+          middleware.description,
+          middleware.taskId,
+          middleware.params,
+          middleware.type,
+          middleware.showOff,
+        );
+      });
+
+      return new StepWorkflowResponseDto(
+        step.id,
+        step.description,
+        step.taskId,
+        step.order,
+        middlewares,
+      );
+    });
+
+    return new WorkflowResponseDto(
+      workflow.id,
+      workflow.name,
+      workflow.description,
+      workflow.status,
+      stepsWorkflow,
+    );
+  }
+
   async getWorkflowVariables(workflowId: string): Promise<string[]> {
     logger.info('Getting workflow variables: ', workflowId);
     const workflow = await this.workFlowAdapter.get(workflowId);
@@ -222,7 +261,7 @@ export class WorkflowUseCase {
   ): Promise<ExecuteWorkflowResponseDto> {
     logger.info(
       'Workflow Use Case - Execute - Executing workflow: ' +
-        JSON.stringify(executeWorkflowRequestDto),
+      JSON.stringify(executeWorkflowRequestDto),
     );
 
     const workflowExecution = await this.workFlowExecutionAdapter.getById(
@@ -266,8 +305,7 @@ export class WorkflowUseCase {
     );
 
     logger.info(
-      `Task result for workflow execution id: ${workflowExecution.id}, step execution id: ${
-        currentStepExecution.id
+      `Task result for workflow execution id: ${workflowExecution.id}, step execution id: ${currentStepExecution.id
       }: ${JSON.stringify(taskResult)}`,
     );
 
@@ -293,8 +331,7 @@ export class WorkflowUseCase {
     ) {
       const routingResponse = combinedMiddlewareResponse['routing-middleware'];
       logger.info(
-        `Workflow Use Case - Execute - Workflow execution ${
-          workflowExecution.id
+        `Workflow Use Case - Execute - Workflow execution ${workflowExecution.id
         } routing response ${JSON.stringify(routingResponse)}`,
       );
 
@@ -543,8 +580,7 @@ export class WorkflowUseCase {
     );
 
     logger.info(
-      `WorkflowUseCase - ExecutePostMiddlewares - executing post middlewares for step: ${
-        stepWorkflow.id
+      `WorkflowUseCase - ExecutePostMiddlewares - executing post middlewares for step: ${stepWorkflow.id
       }, following the middlewares: ${JSON.stringify(postExecutionMiddlewares)}`,
     );
 
@@ -566,8 +602,7 @@ export class WorkflowUseCase {
         );
 
         logger.info(
-          `ExecutePostMiddlewares - mddleware response for middleware: ${
-            middleware.name
+          `ExecutePostMiddlewares - mddleware response for middleware: ${middleware.name
           }: ${JSON.stringify(middlewareResponse)}`,
         );
 
@@ -577,16 +612,14 @@ export class WorkflowUseCase {
             : { middlewareResponse, showOff: middleware.showOff };
       } catch (error) {
         logger.error(
-          `ExecutePostMiddlewares -  error processing middleware ${
-            middleware.name
+          `ExecutePostMiddlewares -  error processing middleware ${middleware.name
           } Error:  ${JSON.stringify(error)}`,
         );
       }
     }
 
     logger.info(
-      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${
-        stepWorkflow.id
+      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${stepWorkflow.id
       }, with the following combined response: ${JSON.stringify(combinedMiddlewareResponse)}`,
     );
 
@@ -599,8 +632,7 @@ export class WorkflowUseCase {
     );
 
     logger.info(
-      `WorkflowUseCase - ExecutePostStartMiddlewares - executing post start middlewares for step: ${
-        stepWorkflow.id
+      `WorkflowUseCase - ExecutePostStartMiddlewares - executing post start middlewares for step: ${stepWorkflow.id
       }, following the middlewares: ${JSON.stringify(postStartMiddlewares)}`,
     );
 
@@ -621,8 +653,7 @@ export class WorkflowUseCase {
         }
 
         logger.info(
-          `WorkflowUseCase - executePostStartMiddlewares - mddleware response for middleware: ${
-            middleware.name
+          `WorkflowUseCase - executePostStartMiddlewares - mddleware response for middleware: ${middleware.name
           }: ${JSON.stringify(middlewareResponse)}`,
         );
 
@@ -652,8 +683,7 @@ export class WorkflowUseCase {
     }
 
     logger.info(
-      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${
-        stepWorkflow.id
+      `WorkflowUseCase - ExecutePostMiddlewares: completed execution of middlewares for step: ${stepWorkflow.id
       }, with the following combined response: ${JSON.stringify(combinedMiddlewareResponse)}`,
     );
 
diff --git a/src/orchestrator/infrastructure/adapters/db/workflow.adapter.ts b/src/orchestrator/infrastructure/adapters/db/workflow.adapter.ts
index c6636fc7..80642b69 100644
--- a/src/orchestrator/infrastructure/adapters/db/workflow.adapter.ts
+++ b/src/orchestrator/infrastructure/adapters/db/workflow.adapter.ts
@@ -2,7 +2,7 @@ import { Injectable } from '@nestjs/common';
 import { WorkflowPort } from '@orchestrator/infrastructure/ports/db/workflow.port';
 import { Workflow } from '@orchestrator/domain/entities/workflow.entity';
 import { DynamoService } from '@common/dynamo/dynamo.service';
-import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
+import { DynamoDBDocumentClient, GetCommand, PutCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
 import { DynamoException } from '@common/exception/types/DynamoException';
 import { RecordStatus } from '@common/enums';
 
@@ -28,11 +28,9 @@ export class WorkFlowAdapter implements WorkflowPort {
       return Promise.resolve(Item.workflow as Workflow);
     } catch (error) {
       const errorResponse = {
-        message: `Error while fetching workflow with id: ${key}. Error: ${
-          error.message || error
-        }. Type: ${error.constructor?.name || 'Unknown'}${
-          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
-        }`,
+        message: `Error while fetching workflow with id: ${key}. Error: ${error.message || error
+          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
+          }`,
         error: error.message || error.toString(),
       };
       throw new DynamoException(errorResponse);
@@ -53,11 +51,9 @@ export class WorkFlowAdapter implements WorkflowPort {
       return Promise.resolve(workflow);
     } catch (error) {
       const errorResponse = {
-        message: `Error while saving workflow with id: ${workflow.id}. Error: ${
-          error.message || error
-        }. Type: ${error.constructor?.name || 'Unknown'}${
-          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
-        }`,
+        message: `Error while saving workflow with id: ${workflow.id}. Error: ${error.message || error
+          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
+          }`,
         error: error.message || error.toString(),
       };
       throw new DynamoException(errorResponse);
@@ -69,4 +65,33 @@ export class WorkFlowAdapter implements WorkflowPort {
 
     return Promise.resolve(this.save(workflow));
   }
+
+  async getByName(name: string): Promise<Workflow> {
+    const params = {
+      TableName: this.DYNAMO_TABLE_NAME,
+      FilterExpression: 'workflow.#name = :name',
+      ExpressionAttributeNames: {
+        '#name': 'name', // Use alias for reserved keyword
+      },
+      ExpressionAttributeValues: {
+        ':name': name,
+      },
+    };
+
+    try {
+      const { Items } = await this.dynamoClient.send(new ScanCommand(params));
+      if (!Items || Items.length === 0) {
+        return Promise.resolve(null);
+      }
+      return Promise.resolve(Items[0].workflow as Workflow);
+    } catch (error) {
+      const errorResponse = {
+        message: `Error while fetching workflow with name: ${name}. Error: ${error.message || error
+          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
+          }`,
+        error: error.message || error.toString(),
+      };
+      throw new DynamoException(errorResponse);
+    }
+  }
 }
diff --git a/src/orchestrator/infrastructure/ports/db/workflow.port.ts b/src/orchestrator/infrastructure/ports/db/workflow.port.ts
index b458ce5e..82016a4a 100644
--- a/src/orchestrator/infrastructure/ports/db/workflow.port.ts
+++ b/src/orchestrator/infrastructure/ports/db/workflow.port.ts
@@ -4,4 +4,5 @@ export interface WorkflowPort {
   get(key: string): Promise<Workflow>;
   save(workflow: Workflow): Promise<Workflow>;
   delete(workflow: Workflow): Promise<Workflow>;
+  getByName(name: string): Promise<Workflow>;
 }
