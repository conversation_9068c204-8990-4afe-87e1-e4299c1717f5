import { Body, Controller, Delete, Get, Param, Post, Put, Version } from '@nestjs/common';
import { AgentUseCase } from '@intelligence/application/use-cases/agent.use-case';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { UpdateAgentDto } from '@intelligence/application/dto/in/update-agent.dto';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { SendDirectMessageDto } from '@intelligence/application/dto/in/send-direct-message.dto';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('intelligence/agents')
export class AgentController {
  constructor(private readonly agentUseCase: AgentUseCase) { }

  @Post()
  @Version('1')
  async create(@Body() createAgentDto: AgentDto): Promise<any> {
    const task = await this.agentUseCase.create(createAgentDto);

    return {
      statusCode: 201,
      data: task,
    };
  }

  @Get('/:agentId')
  @Version('1')
  async findById(@Param('agentId') agentId: string): Promise<any> {
    const agent = await this.agentUseCase.findById(agentId);

    return {
      statusCode: 200,
      data: agent,
    };
  }

  @Get('name/:name/lang/:lang')
  @Version('1')
  async findByNameAndLang(@Param('name') name: string, @Param('lang') lang: string): Promise<any> {
    const agent = await this.agentUseCase.getByNameAndLang(name, lang);

    return {
      statusCode: 200,
      data: agent,
    };
  }

  @Get('/conversation-history/:workfloExecutionId')
  @Version('1')
  async findConversationHistory(
    @Param('workfloExecutionId') workfloExecutionId: string,
  ): Promise<any> {
    const conversationHistory = await this.agentUseCase.getConversationHistory(workfloExecutionId);

    return {
      statusCode: 200,
      data: conversationHistory,
    };
  }

  @Post('/conversation-history/:workflowExecutionId')
  @Version('1')
  async createConversationHistory(
    @Param('workflowExecutionId') workflowExecutionId: string,
    @Body() sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<any> {
    const conversationHistory = await this.agentUseCase.createConversationHistory(
      workflowExecutionId,
      sendDirectMessageDto,
    );

    return {
      statusCode: 200,
      data: conversationHistory,
    };
  }

  @Get()
  @Version('1')
  async findByAll(): Promise<any> {
    const agentListDtoReturn = await this.agentUseCase.findAll();
    return {
      statusCode: 200,
      data: agentListDtoReturn,
    };
  }

  @Put('/:agentId')
  @Version('1')
  async update(
    @Param('agentId') agentId: string,
    @Body() updateAgentDto: UpdateAgentDto,
  ): Promise<any> {
    const updatedAgent = await this.agentUseCase.update(agentId, updateAgentDto);

    return {
      statusCode: 200,
      data: updatedAgent,
    };
  }

  @Delete('/:agentId')
  @Version('1')
  async delete(@Param('agentId') agentId: string): Promise<any> {
    await this.agentUseCase.delete(agentId);
    return {
      statusCode: 200,
    };
  }
}
