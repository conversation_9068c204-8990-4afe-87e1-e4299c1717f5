import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsDate,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsEnum,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { RecordStatus } from '@common/enums';

export enum StatsDataSource {
  CUSTOM_DATA = 'customData',
  MIDDLEWARE = 'middleware',
}

export class StatsFieldConfig {
  @IsEnum(StatsDataSource)
  @IsNotEmpty()
  source: StatsDataSource;

  @IsString()
  @IsNotEmpty()
  path: string;

  constructor(data?: Partial<StatsFieldConfig>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class StatsConfig {
  @IsString()
  @IsNotEmpty()
  workflowId: string;

  @ValidateNested()
  @Type(() => StatsFieldConfig)
  @IsOptional()
  recoveredValueFrom?: StatsFieldConfig;

  @ValidateNested()
  @Type(() => StatsFieldConfig)
  @IsOptional()
  currentDebit?: StatsFieldConfig;

  // Allow additional dynamic properties while maintaining type safety
  [key: string]: any;

  constructor(data?: Partial<StatsConfig>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

// Legacy support - keep for backward compatibility
export class RecoveredValueConfig extends StatsFieldConfig {
  constructor(data?: Partial<RecoveredValueConfig>) {
    super(data);
  }
}

// Legacy enum - keep for backward compatibility
export enum RecoveredValueSource {
  CUSTOM_DATA = 'customData',
  MIDDLEWARE_RESPONSE = 'middlewareResponse',
}

export class TaxRules {
  @IsString()
  @IsOptional()
  penaltyFee?: string;

  @IsString()
  @IsOptional()
  dailyFee?: string;

  constructor(data?: Partial<TaxRules>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomImportConfig {
  @IsString()
  @IsOptional()
  delimiter?: string;

  @ValidateNested()
  @Type(() => TaxRules)
  @IsOptional()
  taxRules?: TaxRules;

  @IsObject()
  @IsOptional()
  headerMapping?: Record<string, string>;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  additionalHeaders?: string[];

  constructor(data?: Partial<CustomImportConfig>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class ImportTransformer {
  @IsString()
  @IsOptional()
  name?: string;

  @IsNumber()
  @IsOptional()
  tax?: number;

  @IsNumber()
  @IsOptional()
  fee?: number;

  constructor(data?: Partial<ImportTransformer>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class PortfolioPreferences {
  @IsUUID('4')
  @IsOptional()
  defaultWorkflowId?: string;

  @IsString()
  @Matches(/^[+-]?\d+(\.\d+)?$/, {
    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
  })
  @IsOptional()
  timezoneUTC?: string;

  @IsString()
  @Matches(
    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
    {
      message: 'importCronExpression must be a valid cron expression',
    },
  )
  @IsOptional()
  importCronExpression?: string;

  @IsUUID('4')
  @IsOptional()
  followUpWorkflowId?: string;

  @IsString()
  @Matches(
    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
    {
      message: 'followUpCronExpression must be a valid cron expression',
    },
  )
  @IsOptional()
  followUpCronExpression?: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  followUpQuantity?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  followUpIntervalMinutes?: number;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  exportColumns?: string[];

  @ValidateNested()
  @Type(() => CustomImportConfig)
  @IsOptional()
  customImportConfig?: CustomImportConfig;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StatsConfig)
  @IsOptional()
  statsConfig?: StatsConfig[];

  @ValidateNested()
  @Type(() => StatsFieldConfig)
  @IsOptional()
  recoveredValueFrom?: StatsFieldConfig;

  constructor(data?: Partial<PortfolioPreferences>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomerPreferencesEntity {
  @IsUUID('4')
  @IsOptional()
  customerId?: string;

  @ValidateNested()
  @Type(() => PortfolioPreferences)
  @IsOptional()
  portfolio?: PortfolioPreferences;

  @IsString()
  @IsOptional()
  test?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(data: Partial<CustomerPreferencesEntity>) {
    // Set required defaults
    this.status = data.status || RecordStatus.ACTIVE;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();

    // Assign all properties dynamically
    Object.assign(this, data);
  }
}
