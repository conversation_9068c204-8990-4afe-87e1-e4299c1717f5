import { RecordStatus } from '@common/enums';
import {
  IsDate,
  IsDecimal,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsPositive,
  IsUUID,
} from 'class-validator';

export class CollectCashStatsEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsDecimal({ decimal_digits: '2' })
  @IsPositive()
  @IsNotEmpty()
  readonly dealValue: number;

  @IsDecimal({ decimal_digits: '2' })
  @IsPositive()
  @IsNotEmpty()
  readonly currentDebit: number;

  @IsDecimal({ decimal_digits: '2' })
  @IsPositive()
  @IsNotEmpty()
  readonly originalDebit: number;

  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  readonly installments: number;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    portfolioId: string,
    portfolioItemId: string,
    workflowId: string,
    dealValue: number,
    currentDebit: number,
    originalDebit: number,
    installments: number,
    status: RecordStatus = RecordStatus.ACTIVE,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.portfolioId = portfolioId;
    this.portfolioItemId = portfolioItemId;
    this.workflowId = workflowId;
    this.dealValue = dealValue;
    this.currentDebit = currentDebit;
    this.originalDebit = originalDebit;
    this.installments = installments;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
