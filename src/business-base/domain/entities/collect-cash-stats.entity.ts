import { RecordStatus } from '@common/enums';
import {
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsPositive,
  IsUUID,
} from 'class-validator';

export class CollectCashStatsEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  readonly dealValue: number;

  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  readonly originalDebt: number;

  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  readonly installments: number;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    customerId: string,
    portfolioId: string,
    portfolioItemId: string,
    workflowId: string,
    dealValue: number,
    originalDebt: number,
    installments: number,
    status: RecordStatus = RecordStatus.ACTIVE,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.customerId = customerId;
    this.portfolioId = portfolioId;
    this.portfolioItemId = portfolioItemId;
    this.workflowId = workflowId;
    this.dealValue = dealValue;
    this.originalDebt = originalDebt;
    this.installments = installments;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
