import { RecordStatus } from '@common/enums';
import {
  IsDate,
  IsDecimal,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsPositive,
  IsUUID,
} from 'class-validator';

export class CollectCashStatsEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsDecimal({ decimal_digits: '2' })
  @IsPositive()
  @IsNotEmpty()
  readonly valorRecuperado: number;

  @IsInt()
  @IsPositive()
  @IsNotEmpty()
  readonly quantidadeDeParcelas: number;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    portfolioId: string,
    portfolioItemId: string,
    workflowId: string,
    valorRecuperado: number,
    quantidadeDeParcelas: number,
    status: RecordStatus = RecordStatus.ACTIVE,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.portfolioId = portfolioId;
    this.portfolioItemId = portfolioItemId;
    this.workflowId = workflowId;
    this.valorRecuperado = valorRecuperado;
    this.quantidadeDeParcelas = quantidadeDeParcelas;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
