import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { Module } from '@nestjs/common';
import { CustomerController } from '@business-base/application/controllers/customer.controller';
import { CustomerAdapter } from '@business-base/infrastructure/adapters/db/customer.adapter';
import { CustomerWorkflowAdapter } from '@business-base/infrastructure/adapters/db/customer-workflow.adapter';
import { CustomerUseCase } from '@business-base/application/use-cases/customer.use-case';
import { UserService } from '@common/auth/user.service';
import { PortfolioAdapter } from '@business-base/infrastructure/adapters/db/portfolio.adapter';
import { PortfolioController } from '@business-base/application/controllers/portfolio.controller';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { PortfolioItemAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item.adapter';
import { PortfolioItemCustomDataAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-custom-data.adapter';
import { PortfolioItemImportErrorAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-import-error.adapter';
import { PortfolioItemWorkflowExecutionAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-workflow-execution.adapter';
import { PortfolioItemExecutionHistoryAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-execution-history.adapter';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { PortfolioItemController } from '@business-base/application/controllers/portfolio-item.controller';
import { PortfolioItemWorkflowExecutionUseCase } from '@business-base/application/use-cases/portfolio-item-workflow-execution.use-case';
import { S3Service } from '@common/s3/s3.service';
import { SQSService } from '@common/sqs/sqs.service';
import { PortfolioJobUpdateImportDataUseCase } from '@business-base/application/use-cases/portfolio-job-update-import-data.use-case';
import { PortfolioWorkflowExecutionUseCase } from '@business-base/application//use-cases/portfolio-workflow-execution.use-case';
import { PortfolioJobExecutionUseCase } from '@business-base/application/use-cases/portfolio-job-execution.use-case';
import { SQSConsumerService } from '@business-base/application/services/sqs-consumers.service';
import { PortfolioJobExecutionRestartSerivce } from '@business-base/application/services/portfolio-job-execution-restart.service';
import { PortfolioItemJobUpdateIdleUseCase } from '@business-base/application/use-cases/portfolio-item-job-update-idle.use-case';
import { InfraWorkflowAdapter } from '@business-base/infrastructure/adapters/http/workflow.adapter';
import { InfraMessageHubAdapter } from '@business-base/infrastructure/adapters/http/message-hub.adapter';
import { PortfolioItemStatusMappingAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-status-mapping.adapter';
import { PortfolioItemStatusMappingController } from '@business-base/application/controllers/portfolio-item-status-mapping.controller';
import { PortfolioItemStatusMappingUseCase } from '@business-base/application/use-cases/portfolio-item-status-mapping.use-case';
import { PortfolioItemExecutionHistoryUseCase } from '@business-base/application/use-cases/portfolio-item-execution-history.use-case';
import { PortfolioItemExecutionHistoryController } from '@business-base/application/controllers/portfolio-item-execution-history.controller';
import { InfraConversationHistoryAdapter } from '@business-base/infrastructure/adapters/http/conversation-history.adapter';
import { MulterModule } from '@nestjs/platform-express';
import * as multer from 'multer';
import { SlackMessage } from '@edutalent/commons-sdk';
import { NectarAdapter } from '@business-base/infrastructure/adapters/soap/nectar.adapter';
import { MiddlewareResponseOutputAdapter } from '@business-base/infrastructure/adapters/db/middleware-response-output.adapter';
import { MetricsController } from '@business-base/application/controllers/metric.controller';
import { MetricUseCase } from '@business-base/application/use-cases/metric.use-case';
import { PortfolioItemFollowFlowExecutionUseCase } from '@business-base/application/use-cases/portfolio-item-followflow-execution.use-case';
import { CustomerChannelIntegrationDataDefinitionAdapter } from '@common/auth/db/adapters/customer-channel-integration-data-definition.adapter';
import { MessageHubOutgoingMessageAdapter } from '@business-base/infrastructure/adapters/db/message-hub-outgoing-message.adapter';
import { PortfolioItemScheduledFollowUpAdapter } from '@business-base/infrastructure/adapters/db/portfolio-item-scheduled-follow-up.adapter';
import { CustomerPreferencesController } from '@business-base/application/controllers/customer-preferences.controller';
import { CustomerPreferencesAdapter } from '@business-base/infrastructure/adapters/db/customer-preferences.adapter';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { CollectCashStatsAdapter } from '@business-base/infrastructure/adapters/db/collect-cash-stats.adapter';
import { StatisticalDataUseCase } from './application/use-cases/statistical-data.use-case';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 50000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [
    httpModule,
    ScheduleModule.forRoot(),
    MulterModule.register({
      storage: multer.memoryStorage(),
    }),
  ],
  providers: [
    { provide: 'CustomerPort', useClass: CustomerAdapter },
    { provide: 'PortfolioPort', useClass: PortfolioAdapter },
    { provide: 'PortfolioItemPort', useClass: PortfolioItemAdapter },
    { provide: 'PortfolioItemCustomDataPort', useClass: PortfolioItemCustomDataAdapter },
    { provide: 'MiddlewareResponseOutputPort', useClass: MiddlewareResponseOutputAdapter },
    { provide: 'PortfolioItemImportErrorPort', useClass: PortfolioItemImportErrorAdapter },
    {
      provide: 'PortfolioItemWorkflowExecutionPort',
      useClass: PortfolioItemWorkflowExecutionAdapter,
    },
    {
      provide: 'PortfolioItemScheduledFollowUpPort',
      useClass: PortfolioItemScheduledFollowUpAdapter,
    },
    { provide: 'CustomerWorkflowPort', useClass: CustomerWorkflowAdapter },
    {
      provide: 'PortfolioItemExecutionHistoryPort',
      useClass: PortfolioItemExecutionHistoryAdapter,
    },
    {
      provide: 'InfraWorkflowPort',
      useClass: InfraWorkflowAdapter,
    },
    {
      provide: 'InfraMessageHubPort',
      useClass: InfraMessageHubAdapter,
    },
    {
      provide: 'PortfolioItemStatusMappingPort',
      useClass: PortfolioItemStatusMappingAdapter,
    },
    {
      provide: 'InfraConversationHistoryPort',
      useClass: InfraConversationHistoryAdapter,
    },
    {
      provide: 'NectarPort',
      useClass: NectarAdapter,
    },
    {
      provide: 'CustomerChannelIntegrationDataDefinitionPort',
      useClass: CustomerChannelIntegrationDataDefinitionAdapter,
    },
    DynamoService,
    S3Service,
    SQSService,
    UserService,
    CustomerUseCase,
    PortfolioUseCase,
    PortfolioItemUseCase,
    SQSConsumerService,
    SlackMessage,
    PortfolioJobUpdateImportDataUseCase,
    PortfolioWorkflowExecutionUseCase,
    PortfolioJobExecutionUseCase,
    PortfolioJobExecutionRestartSerivce,
    PortfolioItemJobUpdateIdleUseCase,
    PortfolioItemWorkflowExecutionUseCase,
    PortfolioItemFollowFlowExecutionUseCase,
    PortfolioItemStatusMappingUseCase,
    PortfolioItemExecutionHistoryUseCase,
    MetricUseCase,
    {
      provide: 'MessageHubOutgoingMessagePort',
      useClass: MessageHubOutgoingMessageAdapter,
    },
    {
      provide: 'CustomerPreferencesPort',
      useClass: CustomerPreferencesAdapter,
    },
    {
      provide: 'CollectCashStatsPort',
      useClass: CollectCashStatsAdapter,
    },
    CustomerPreferencesUseCase,
    StatisticalDataUseCase
  ],
  controllers: [
    CustomerController,
    PortfolioController,
    PortfolioItemController,
    PortfolioItemStatusMappingController,
    PortfolioItemExecutionHistoryController,
    MetricsController,
    CustomerPreferencesController,
  ],
})
export class BusinessBaseModule { }
