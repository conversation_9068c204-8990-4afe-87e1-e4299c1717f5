import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { BusinessException } from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { RecordStatus } from '@common/enums';
import { StatsConfigDto, StatsDataSource, StatsFieldConfigDto } from '../dto/customer-preferences.dto';
import * as crypto from 'crypto';
import { PortfolioItemEntity } from '../../domain/entities/portfolio-item.entity';

export interface RecoveredValueSummary {
  portfolioId: string;
  portfolioName: string;
  totalRecoveredValue: number;
  itemCount: number;
}

export interface CustomerRecoveredValueSummary {
  customerId: string;
  totalRecoveredValue: number;
  portfolios: RecoveredValueSummary[];
}

@Injectable()
export class StatisticalDataUseCase {
  constructor(
    @Inject('CollectCashStatsPort')
    private readonly collectCashStatsAdapter: CollectCashStatsPort,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
  ) { }

  /**
   * Extracts and stores statistical data for a portfolio item based on workflow configuration
   */
  async extractAndStoreStatisticalData(
    customerId: string,
    portfolioId: string,
    portfolioItem: PortfolioItemEntity,
    statsConfig: StatsConfigDto[],
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Extracting and storing statistical data', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId: portfolioItem.id,
        operation: 'extractAndStoreStatisticalData',
        layer: 'USE_CASE',
      });

      const workflows = statsConfig.map(config => config.workflowId);

      for (const workflowId of workflows) {
        const workflowConfig = statsConfig.find(config => config.workflowId === workflowId);
        if (!workflowConfig) {
          logger.info('No workflow-specific statistical configuration found', {
            traceId,
            workflowId,
            portfolioItemId: portfolioItem.id,
            operation: 'extractAndStoreStatisticalData',
            layer: 'USE_CASE',
          });
          continue;
        }

        // Extract statistical values
        const extractedData = await this.extractStatisticalValues(workflowConfig, portfolioItem);

        // Only store if we have valid data
        if (extractedData.dealValue > 0 || extractedData.currentDebt > 0 || extractedData.originalDebt > 0) {
          const collectCashStats = new CollectCashStatsEntity(
            customerId,
            portfolioId,
            portfolioItem.id,
            workflowId,
            extractedData.dealValue,
            extractedData.currentDebt,
            extractedData.originalDebt,
            1, // Default to 1 installment
            RecordStatus.ACTIVE,
          );

          await this.collectCashStatsAdapter.create(collectCashStats);

          logger.info('Statistical data stored successfully', {
            traceId,
            customerId,
            portfolioId,
            portfolioItemId: portfolioItem.id,
            workflowId,
            dealValue: extractedData.dealValue,
            currentDebt: extractedData.currentDebt,
            originalDebt: extractedData.originalDebt,
            operation: 'extractAndStoreStatisticalData',
            layer: 'USE_CASE',
          });
        } else {
          logger.info('No valid statistical data to store', {
            traceId,
            portfolioItemId: portfolioItem.id,
            workflowId,
            operation: 'extractAndStoreStatisticalData',
            layer: 'USE_CASE',
          });
        }
      }
    } catch (error) {
      logger.error('Error extracting and storing statistical data', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId: portfolioItem.id,
        error: JSON.stringify(error),
        operation: 'extractAndStoreStatisticalData',
        layer: 'USE_CASE',
      });
    }
  }

  /**
   * Extracts statistical values from a workflow configuration
   */
  private async extractStatisticalValues(
    workflowConfig: StatsConfigDto,
    portfolioItem: PortfolioItemEntity,
  ): Promise<{
    dealValue: number;
    currentDebt: number;
    originalDebt: number;
  }> {
    const traceId = CorrelationContextService.getTraceId();
    const result = { dealValue: 0, currentDebt: 0, originalDebt: 0 };

    try {
      // Extract dealValue from recoveredValueFrom
      if (workflowConfig.recoveredValueFrom) {
        result.dealValue = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.recoveredValueFrom,
          'recoveredValueFrom',
        );
      }

      // Extract currentDebt
      if (workflowConfig.currentDebt) {
        result.currentDebt = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.currentDebt,
          'currentDebt',
        );
      }

      // Extract originalDebt
      if (workflowConfig.originalDebt) {
        result.originalDebt = await this.extractFieldValue(
          portfolioItem,
          workflowConfig.originalDebt,
          'originalDebt',
        );
      }

      logger.info('Statistical values extracted successfully', {
        traceId,
        portfolioItemId: portfolioItem.id,
        workflowId: workflowConfig.workflowId,
        dealValue: result.dealValue,
        currentDebt: result.currentDebt,
        originalDebt: result.originalDebt,
        operation: 'extractStatisticalValues',
        layer: 'USE_CASE',
      });

      return result;
    } catch (error) {
      logger.error('Error extracting statistical values', {
        traceId,
        portfolioItemId: portfolioItem.id,
        workflowId: workflowConfig.workflowId,
        error: JSON.stringify(error),
        operation: 'extractStatisticalValues',
        layer: 'USE_CASE',
      });
      return result; // Return zeros on error
    }
  }

  /**
   * Extracts a single field value from the specified data source
   */
  private async extractFieldValue(
    portfolioItem: PortfolioItemEntity,
    fieldConfig: StatsFieldConfigDto,
    fieldName: string,
  ): Promise<number> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      let data: any;


      if (fieldConfig.source === StatsDataSource.CUSTOM_DATA) {
        const customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
          portfolioItem.customDataId,
          portfolioItem.id,
        );
        data = customData?.customData;
      } else if (fieldConfig.source === StatsDataSource.MIDDLEWARE) {
        if (!portfolioItem.middlewareResponseOutputId) {
          logger.info('No middleware response found for portfolio item', {
            traceId,
            portfolioItemId: portfolioItem.id,
            fieldName,
            operation: 'extractFieldValue',
            layer: 'USE_CASE',
          });
          return 0;
        }

        const middlewareResponse = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          portfolioItem.middlewareResponseOutputId,
          portfolioItem.id,
        );
        data = middlewareResponse?.data;
      }

      if (!data) {
        logger.info('No data found for field extraction', {
          traceId,
          portfolioItemId: portfolioItem.id,
          fieldName,
          source: fieldConfig.source,
          operation: 'extractFieldValue',
          layer: 'USE_CASE',
        });
        return 0;
      }

      // Extract value using the path
      const value = this.getValueFromPath(data, fieldConfig.path);
      const numericValue = this.parseNumericValue(value);

      logger.info('Field value extracted successfully', {
        traceId,
        portfolioItemId: portfolioItem.id,
        fieldName,
        source: fieldConfig.source,
        path: fieldConfig.path,
        extractedValue: numericValue,
        operation: 'extractFieldValue',
        layer: 'USE_CASE',
      });

      return numericValue;
    } catch (error) {
      logger.error('Error extracting field value', {
        traceId,
        portfolioItemId: portfolioItem.id,
        fieldName,
        source: fieldConfig.source,
        path: fieldConfig.path,
        error: JSON.stringify(error),
        operation: 'extractFieldValue',
        layer: 'USE_CASE',
      });
      return 0;
    }
  }

  /**
   * Extracts value from nested object using dot notation or bracket notation path
   */
  private getValueFromPath(data: any, path: string): any {
    try {
      // Handle bracket notation like ['deal-info'].VALOR_RECUPERADO
      if (path.includes('[') && path.includes(']')) {
        return Function(`"use strict"; return (${JSON.stringify(data)})${path}`)();
      }

      // Handle simple dot notation
      return path.split('.').reduce((obj, key) => obj?.[key], data);
    } catch (error) {
      logger.warn('Error parsing path, returning undefined', {
        path,
        error: JSON.stringify(error),
        operation: 'getValueFromPath',
        layer: 'USE_CASE',
      });
      return undefined;
    }
  }

  /**
   * Parses a value to a numeric format, handling Brazilian currency format
   */
  private parseNumericValue(value: any): number {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      // Handle Brazilian currency format (e.g., "2.750,50" or "2,750.50")
      const cleanValue = value
        .replace(/[^\d,.-]/g, '') // Remove non-numeric characters except comma, dot, and minus
        .replace(/\./g, '') // Remove thousand separators (dots)
        .replace(',', '.'); // Replace decimal comma with dot

      const parsed = parseFloat(cleanValue);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  }

}
