import { Test, TestingModule } from '@nestjs/testing';
import { RecoveredValueUseCase } from './recovered-value.use-case';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { BusinessException } from '@common/exception/types/BusinessException';
import { RecordStatus } from '@common/enums';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { CustomerPreferencesEntity, RecoveredValueSource } from '@business-base/domain/entities/customer-preferences.entity';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';

describe('RecoveredValueUseCase', () => {
  let useCase: RecoveredValueUseCase;
  let collectCashStatsAdapter: jest.Mocked<CollectCashStatsPort>;
  let customerPreferencesAdapter: jest.Mocked<CustomerPreferencesPort>;
  let portfolioItemAdapter: jest.Mocked<PortfolioItemPort>;
  let portfolioItemCustomDataAdapter: jest.Mocked<PortfolioItemCustomDataPort>;
  let middlewareResponseOutputAdapter: jest.Mocked<MiddlewareResponseOutputPort>;
  let portfolioAdapter: jest.Mocked<PortfolioPort>;

  const mockCustomerId = '4cd6d515-2604-4c2c-adad-435acbef1f5c';
  const mockPortfolioId = '6f413811-4aa8-43f4-8c48-d00143dd226d';
  const mockPortfolioItemId = '7f413811-4aa8-43f4-8c48-d00143dd226e';
  const mockWorkflowId = '8f413811-4aa8-43f4-8c48-d00143dd226f';

  beforeEach(async () => {
    const mockCollectCashStatsAdapter = {
      findByCustomerIdWithDateRange: jest.fn(),
      findByPortfolioIdWithDateRange: jest.fn(),
      create: jest.fn(),
      getTotalRecoveredValueByCustomerId: jest.fn(),
      getTotalRecoveredValueByPortfolioId: jest.fn(),
    };

    const mockCustomerPreferencesAdapter = {
      getById: jest.fn(),
    };

    const mockPortfolioItemAdapter = {
      get: jest.fn(),
    };

    const mockPortfolioItemCustomDataAdapter = {
      getByPortfolioItemId: jest.fn(),
    };

    const mockMiddlewareResponseOutputAdapter = {
      getByPortfolioItemId: jest.fn(),
    };

    const mockPortfolioAdapter = {
      getAll: jest.fn(),
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecoveredValueUseCase,
        { provide: 'CollectCashStatsPort', useValue: mockCollectCashStatsAdapter },
        { provide: 'CustomerPreferencesPort', useValue: mockCustomerPreferencesAdapter },
        { provide: 'PortfolioItemPort', useValue: mockPortfolioItemAdapter },
        { provide: 'PortfolioItemCustomDataPort', useValue: mockPortfolioItemCustomDataAdapter },
        { provide: 'MiddlewareResponseOutputPort', useValue: mockMiddlewareResponseOutputAdapter },
        { provide: 'PortfolioPort', useValue: mockPortfolioAdapter },
      ],
    }).compile();

    useCase = module.get<RecoveredValueUseCase>(RecoveredValueUseCase);
    collectCashStatsAdapter = module.get('CollectCashStatsPort');
    customerPreferencesAdapter = module.get('CustomerPreferencesPort');
    portfolioItemAdapter = module.get('PortfolioItemPort');
    portfolioItemCustomDataAdapter = module.get('PortfolioItemCustomDataPort');
    middlewareResponseOutputAdapter = module.get('MiddlewareResponseOutputPort');
    portfolioAdapter = module.get('PortfolioPort');
  });

  describe('getRecoveredValuesByCustomerId', () => {
    it('should return customer recovered values summary', async () => {
      const mockPortfolios = [
        { id: mockPortfolioId, name: 'Test Portfolio', customerId: mockCustomerId } as PortfolioEntity,
      ];

      const mockStats = [
        new CollectCashStatsEntity(
          'stat1',
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
          1500.50,
          1,
          RecordStatus.ACTIVE,
        ),
      ];

      portfolioAdapter.getAll.mockResolvedValue(mockPortfolios);
      collectCashStatsAdapter.findByPortfolioIdWithDateRange.mockResolvedValue(mockStats);

      const result = await useCase.getRecoveredValuesByCustomerId(mockCustomerId);

      expect(result.customerId).toBe(mockCustomerId);
      expect(result.totalRecoveredValue).toBe(1500.50);
      expect(result.portfolios).toHaveLength(1);
      expect(result.portfolios[0].portfolioId).toBe(mockPortfolioId);
      expect(result.portfolios[0].totalRecoveredValue).toBe(1500.50);
    });

    it('should handle date filtering', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-12-31');
      const mockPortfolios = [
        { id: mockPortfolioId, name: 'Test Portfolio', customerId: mockCustomerId } as PortfolioEntity,
      ];

      portfolioAdapter.getAll.mockResolvedValue(mockPortfolios);
      collectCashStatsAdapter.findByPortfolioIdWithDateRange.mockResolvedValue([]);

      await useCase.getRecoveredValuesByCustomerId(mockCustomerId, startDate, endDate);

      expect(collectCashStatsAdapter.findByPortfolioIdWithDateRange).toHaveBeenCalledWith(
        mockPortfolioId,
        startDate,
        endDate,
      );
    });

    it('should handle errors gracefully', async () => {
      portfolioAdapter.getAll.mockRejectedValue(new Error('Database error'));

      await expect(useCase.getRecoveredValuesByCustomerId(mockCustomerId)).rejects.toThrow(
        BusinessException,
      );
    });
  });

  describe('getRecoveredValuesByPortfolioId', () => {
    it('should return portfolio recovered values summary', async () => {
      const mockPortfolio = { id: mockPortfolioId, name: 'Test Portfolio' } as PortfolioEntity;
      const mockStats = [
        new CollectCashStatsEntity(
          'stat1',
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
          2500.75,
          1,
          RecordStatus.ACTIVE,
        ),
      ];

      portfolioAdapter.get.mockResolvedValue(mockPortfolio);
      collectCashStatsAdapter.findByPortfolioIdWithDateRange.mockResolvedValue(mockStats);

      const result = await useCase.getRecoveredValuesByPortfolioId(mockPortfolioId);

      expect(result.portfolioId).toBe(mockPortfolioId);
      expect(result.portfolioName).toBe('Test Portfolio');
      expect(result.totalRecoveredValue).toBe(2500.75);
      expect(result.itemCount).toBe(1);
    });
  });

  describe('extractAndStoreRecoveredValue', () => {
    it('should extract and store recovered value from customData', async () => {
      const mockCustomerPreferences = {
        portfolio: {
          recoveredValueFrom: {
            source: RecoveredValueSource.CUSTOM_DATA,
            path: 'VALOR_RECEBIDO',
          },
        },
      } as CustomerPreferencesEntity;

      const mockCustomData = {
        customData: {
          VALOR_RECEBIDO: '1,250.50',
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(mockCustomData);
      collectCashStatsAdapter.create.mockResolvedValue({} as CollectCashStatsEntity);

      await useCase.extractAndStoreRecoveredValue(
        mockCustomerId,
        mockPortfolioId,
        mockPortfolioItemId,
        mockWorkflowId,
      );

      expect(collectCashStatsAdapter.create).toHaveBeenCalledWith(
        expect.objectContaining({
          customerId: mockCustomerId,
          portfolioId: mockPortfolioId,
          portfolioItemId: mockPortfolioItemId,
          workflowId: mockWorkflowId,
          valorRecuperado: 1250.50,
          quantidadeDeParcelas: 1,
        }),
      );
    });

    it('should extract and store recovered value from middlewareResponse', async () => {
      const mockCustomerPreferences = {
        portfolio: {
          recoveredValueFrom: {
            source: RecoveredValueSource.MIDDLEWARE_RESPONSE,
            path: 'acordo-info.valorTotalAcordo',
          },
        },
      } as CustomerPreferencesEntity;

      const mockPortfolioItem = {
        middlewareResponseOutputId: 'middleware-id',
      } as PortfolioItemEntity;

      const mockMiddlewareResponse = {
        data: {
          'acordo-info': {
            valorTotalAcordo: 3500.25,
          },
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
      portfolioItemAdapter.get.mockResolvedValue(mockPortfolioItem);
      middlewareResponseOutputAdapter.getByPortfolioItemId.mockResolvedValue(mockMiddlewareResponse);
      collectCashStatsAdapter.create.mockResolvedValue({} as CollectCashStatsEntity);

      await useCase.extractAndStoreRecoveredValue(
        mockCustomerId,
        mockPortfolioId,
        mockPortfolioItemId,
        mockWorkflowId,
      );

      expect(collectCashStatsAdapter.create).toHaveBeenCalledWith(
        expect.objectContaining({
          valorRecuperado: 3500.25,
        }),
      );
    });

    it('should handle missing customer preferences gracefully', async () => {
      customerPreferencesAdapter.getById.mockResolvedValue(null);

      await useCase.extractAndStoreRecoveredValue(
        mockCustomerId,
        mockPortfolioId,
        mockPortfolioItemId,
        mockWorkflowId,
      );

      expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
    });

    it('should handle zero recovered values', async () => {
      const mockCustomerPreferences = {
        portfolio: {
          recoveredValueFrom: {
            source: RecoveredValueSource.CUSTOM_DATA,
            path: 'VALOR_RECEBIDO',
          },
        },
      } as CustomerPreferencesEntity;

      const mockCustomData = {
        customData: {
          VALOR_RECEBIDO: '0',
        },
      };

      customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(mockCustomData);

      await useCase.extractAndStoreRecoveredValue(
        mockCustomerId,
        mockPortfolioId,
        mockPortfolioItemId,
        mockWorkflowId,
      );

      expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
    });

    it('should handle missing data gracefully', async () => {
      const mockCustomerPreferences = {
        portfolio: {
          recoveredValueFrom: {
            source: RecoveredValueSource.CUSTOM_DATA,
            path: 'VALOR_RECEBIDO',
          },
        },
      } as CustomerPreferencesEntity;

      customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
      portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(null);

      await useCase.extractAndStoreRecoveredValue(
        mockCustomerId,
        mockPortfolioId,
        mockPortfolioItemId,
        mockWorkflowId,
      );

      expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    describe('parseNumericValue', () => {
      it('should handle various currency formats', async () => {
        const testCases = [
          { input: 'R$ 1.500,50', expected: 1500.50 },
          { input: '1,250.75', expected: 1250.75 },
          { input: '2500', expected: 2500 },
          { input: '3.500,25', expected: 3500.25 },
          { input: 'R$1500,50', expected: 1500.50 },
          { input: '  1.200,00  ', expected: 1200.00 },
          { input: 'invalid', expected: 0 },
          { input: '', expected: 0 },
          { input: null, expected: 0 },
          { input: undefined, expected: 0 },
          { input: 1500.50, expected: 1500.50 },
        ];

        for (const testCase of testCases) {
          // Access private method through reflection for testing
          const result = (useCase as any).parseNumericValue(testCase.input);
          expect(result).toBeCloseTo(testCase.expected, 2);
        }
      });
    });

    describe('getNestedProperty', () => {
      it('should handle nested property access', async () => {
        const testData = {
          level1: {
            level2: {
              level3: 'value',
            },
          },
          array: [{ item: 'arrayValue' }],
        };

        // Access private method through reflection for testing
        const getValue = (path: string) => (useCase as any).getNestedProperty(testData, path);

        expect(getValue('level1.level2.level3')).toBe('value');
        expect(getValue('level1.level2')).toEqual({ level3: 'value' });
        expect(getValue('nonexistent')).toBeUndefined();
        expect(getValue('level1.nonexistent')).toBeUndefined();
        expect(getValue('level1.level2.level3.nonexistent')).toBeUndefined();
      });

      it('should handle empty or invalid paths', async () => {
        const testData = { value: 'test' };
        const getValue = (path: string) => (useCase as any).getNestedProperty(testData, path);

        expect(getValue('')).toBe(testData);
        expect(getValue('value')).toBe('test');
      });
    });

    describe('extractRecoveredValue error handling', () => {
      it('should handle database connection errors', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.CUSTOM_DATA,
              path: 'VALOR_RECEBIDO',
            },
          },
        } as CustomerPreferencesEntity;

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemCustomDataAdapter.getByPortfolioItemId.mockRejectedValue(
          new Error('Database connection failed'),
        );

        await useCase.extractAndStoreRecoveredValue(
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
        );

        expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
      });

      it('should handle malformed middleware response data', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.MIDDLEWARE_RESPONSE,
              path: 'acordo-info.valorTotalAcordo',
            },
          },
        } as CustomerPreferencesEntity;

        const mockPortfolioItem = {
          middlewareResponseOutputId: 'middleware-id',
        } as PortfolioItemEntity;

        const mockMiddlewareResponse = {
          data: {
            'acordo-info': null, // Malformed data
          },
        };

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemAdapter.get.mockResolvedValue(mockPortfolioItem);
        middlewareResponseOutputAdapter.getByPortfolioItemId.mockResolvedValue(mockMiddlewareResponse);

        await useCase.extractAndStoreRecoveredValue(
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
        );

        expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
      });

      it('should handle missing middleware response output ID', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.MIDDLEWARE_RESPONSE,
              path: 'acordo-info.valorTotalAcordo',
            },
          },
        } as CustomerPreferencesEntity;

        const mockPortfolioItem = {
          middlewareResponseOutputId: null, // Missing ID
        } as PortfolioItemEntity;

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemAdapter.get.mockResolvedValue(mockPortfolioItem);

        await useCase.extractAndStoreRecoveredValue(
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
        );

        expect(middlewareResponseOutputAdapter.getByPortfolioItemId).not.toHaveBeenCalled();
        expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
      });

      it('should handle invalid path configurations', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.CUSTOM_DATA,
              path: 'deeply.nested.nonexistent.path',
            },
          },
        } as CustomerPreferencesEntity;

        const mockCustomData = {
          customData: {
            VALOR_RECEBIDO: '1,500.50',
          },
        };

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(mockCustomData);

        await useCase.extractAndStoreRecoveredValue(
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
        );

        expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
      });

      it('should handle negative recovered values', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.CUSTOM_DATA,
              path: 'VALOR_RECEBIDO',
            },
          },
        } as CustomerPreferencesEntity;

        const mockCustomData = {
          customData: {
            VALOR_RECEBIDO: '-500.00', // Negative value
          },
        };

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(mockCustomData);

        await useCase.extractAndStoreRecoveredValue(
          mockCustomerId,
          mockPortfolioId,
          mockPortfolioItemId,
          mockWorkflowId,
        );

        // Should not create entry for negative values
        expect(collectCashStatsAdapter.create).not.toHaveBeenCalled();
      });

      it('should handle collectCashStatsAdapter.create failures', async () => {
        const mockCustomerPreferences = {
          portfolio: {
            recoveredValueFrom: {
              source: RecoveredValueSource.CUSTOM_DATA,
              path: 'VALOR_RECEBIDO',
            },
          },
        } as CustomerPreferencesEntity;

        const mockCustomData = {
          customData: {
            VALOR_RECEBIDO: '1,500.50',
          },
        };

        customerPreferencesAdapter.getById.mockResolvedValue(mockCustomerPreferences);
        portfolioItemCustomDataAdapter.getByPortfolioItemId.mockResolvedValue(mockCustomData);
        collectCashStatsAdapter.create.mockRejectedValue(new Error('Database write failed'));

        await expect(
          useCase.extractAndStoreRecoveredValue(
            mockCustomerId,
            mockPortfolioId,
            mockPortfolioItemId,
            mockWorkflowId,
          ),
        ).rejects.toThrow(BusinessException);
      });
    });

    describe('Network and timeout scenarios', () => {
      it('should handle adapter timeout errors', async () => {
        portfolioAdapter.getAll.mockRejectedValue(new Error('Request timeout'));

        await expect(useCase.getRecoveredValuesByCustomerId(mockCustomerId)).rejects.toThrow(
          BusinessException,
        );
      });

      it('should handle concurrent access scenarios', async () => {
        const mockPortfolios = [
          { id: mockPortfolioId, name: 'Test Portfolio', customerId: mockCustomerId } as PortfolioEntity,
        ];

        portfolioAdapter.getAll.mockResolvedValue(mockPortfolios);
        collectCashStatsAdapter.findByPortfolioIdWithDateRange.mockResolvedValue([]);

        // Simulate concurrent calls
        const promises = Array(5).fill(null).map(() =>
          useCase.getRecoveredValuesByCustomerId(mockCustomerId)
        );

        const results = await Promise.all(promises);

        results.forEach(result => {
          expect(result.customerId).toBe(mockCustomerId);
          expect(result.totalRecoveredValue).toBe(0);
        });
      });
    });
  });
});
