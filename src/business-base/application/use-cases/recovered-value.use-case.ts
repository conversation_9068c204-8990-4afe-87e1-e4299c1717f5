import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { BusinessException, BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { RecoveredValueSource } from '@business-base/domain/entities/customer-preferences.entity';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { RecordStatus } from '@common/enums';

export interface RecoveredValueSummary {
  portfolioId: string;
  portfolioName: string;
  totalRecoveredValue: number;
  itemCount: number;
}

export interface CustomerRecoveredValueSummary {
  customerId: string;
  totalRecoveredValue: number;
  portfolios: RecoveredValueSummary[];
}

@Injectable()
export class RecoveredValueUseCase {
  constructor(
    @Inject('CollectCashStatsPort')
    private readonly collectCashStatsAdapter: CollectCashStatsPort,
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
  ) { }

  async getRecoveredValuesByCustomerId(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CustomerRecoveredValueSummary> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Getting recovered values by customer ID', {
        traceId,
        customerId,
        startDate,
        endDate,
        operation: 'getRecoveredValuesByCustomerId',
        layer: 'USE_CASE',
      });

      // Get customer portfolios
      const portfolios = await this.portfolioAdapter.getAll({ customerId });

      const portfolioSummaries: RecoveredValueSummary[] = [];
      let totalRecoveredValue = 0;

      for (const portfolio of portfolios) {
        const portfolioRecoveredValue = await this.getRecoveredValuesByPortfolioId(
          portfolio.id,
          startDate,
          endDate,
        );

        if (portfolioRecoveredValue.totalRecoveredValue > 0) {
          portfolioSummaries.push(portfolioRecoveredValue);
          totalRecoveredValue += portfolioRecoveredValue.totalRecoveredValue;
        }
      }

      return {
        customerId,
        totalRecoveredValue,
        portfolios: portfolioSummaries,
      };
    } catch (error) {
      logger.error('Error getting recovered values by customer ID', {
        traceId,
        customerId,
        error: JSON.stringify(error),
        operation: 'getRecoveredValuesByCustomerId',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'getRecoveredValuesByCustomerId',
        error,
      );
    }
  }

  async getRecoveredValuesByPortfolioId(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<RecoveredValueSummary> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Getting recovered values by portfolio ID', {
        traceId,
        portfolioId,
        startDate,
        endDate,
        operation: 'getRecoveredValuesByPortfolioId',
        layer: 'USE_CASE',
      });

      const portfolio = await this.portfolioAdapter.get(portfolioId);
      const stats = await this.collectCashStatsAdapter.findByPortfolioIdWithDateRange(
        portfolioId,
        startDate,
        endDate,
      );

      const totalRecoveredValue = stats.reduce((sum, stat) => sum + stat.valorRecuperado, 0);

      return {
        portfolioId,
        portfolioName: portfolio.name,
        totalRecoveredValue,
        itemCount: stats.length,
      };
    } catch (error) {
      logger.error('Error getting recovered values by portfolio ID', {
        traceId,
        portfolioId,
        error: JSON.stringify(error),
        operation: 'getRecoveredValuesByPortfolioId',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'getRecoveredValuesByPortfolioId',
        error,
      );
    }
  }

  async extractAndStoreRecoveredValue(
    customerId: string,
    portfolioId: string,
    portfolioItemId: string,
    workflowId: string,
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Extracting and storing recovered value', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId,
        workflowId,
        operation: 'extractAndStoreRecoveredValue',
        layer: 'USE_CASE',
      });

      // Get customer preferences to determine data source
      const customerPreferences = await this.customerPreferencesAdapter.getById(customerId);

      if (!customerPreferences?.portfolio?.recoveredValueFrom) {
        logger.info('No recovered value configuration found for customer', {
          traceId,
          customerId,
          operation: 'extractAndStoreRecoveredValue',
          layer: 'USE_CASE',
        });
        return;
      }

      const config = customerPreferences.portfolio.recoveredValueFrom;
      const recoveredValue = await this.extractRecoveredValue(
        portfolioItemId,
        config.source,
        config.path,
      );

      if (recoveredValue > 0) {
        // Create collect cash stats entry
        const collectCashStats = new CollectCashStatsEntity(
          crypto.randomUUID(),
          customerId,
          portfolioId,
          portfolioItemId,
          workflowId,
          recoveredValue,
          1, // Default to 1 installment
          RecordStatus.ACTIVE,
        );

        await this.collectCashStatsAdapter.create(collectCashStats);

        logger.info('Recovered value stored successfully', {
          traceId,
          customerId,
          portfolioId,
          portfolioItemId,
          recoveredValue,
          operation: 'extractAndStoreRecoveredValue',
          layer: 'USE_CASE',
        });
      }
    } catch (error) {
      logger.error('Error extracting and storing recovered value', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId,
        error: JSON.stringify(error),
        operation: 'extractAndStoreRecoveredValue',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'extractAndStoreRecoveredValue',
        error,
      );
    }
  }

  private async extractRecoveredValue(
    portfolioItemId: string,
    source: RecoveredValueSource,
    path: string,
  ): Promise<number> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      let data: any;

      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
      if (source === RecoveredValueSource.CUSTOM_DATA) {
        const customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
          portfolioItem.customDataId,
          portfolioItemId,
        );
        data = customData?.customData;
      } else if (source === RecoveredValueSource.MIDDLEWARE_RESPONSE) {
        if (portfolioItem.middlewareResponseOutputId) {
          const middlewareResponse = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
            portfolioItem.middlewareResponseOutputId,
            portfolioItemId,
          );
          data = middlewareResponse?.data;
        }
      }

      if (!data) {
        logger.info('No data found for recovered value extraction from middleware response', {
          traceId,
          portfolioItemId,
          source,
          path,
          middlewareResponseOutputId: portfolioItem.middlewareResponseOutputId,
          operation: 'extractRecoveredValue',
          layer: 'USE_CASE',
        });
        return 0;
      }

      const value = this.getNestedProperty(data, path);
      const numericValue = this.parseNumericValue(value);

      logger.info('Recovered value extracted', {
        traceId,
        portfolioItemId,
        source,
        path,
        extractedValue: value,
        numericValue,
        operation: 'extractRecoveredValue',
        layer: 'USE_CASE',
      });

      return numericValue;
    } catch (error) {
      logger.error('Error extracting recovered value', {
        traceId,
        portfolioItemId,
        source,
        path,
        error: JSON.stringify(error),
        operation: 'extractRecoveredValue',
        layer: 'USE_CASE',
      });
      return 0;
    }
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  private parseNumericValue(value: any): number {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      // Remove common currency symbols and formatting
      const cleanValue = value
        .replace(/[R$\s]/g, '') // Remove R$, spaces
        .replace(/\./g, '') // Remove thousands separators (dots)
        .replace(',', '.'); // Replace decimal comma with dot

      const parsed = parseFloat(cleanValue);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  }
}
