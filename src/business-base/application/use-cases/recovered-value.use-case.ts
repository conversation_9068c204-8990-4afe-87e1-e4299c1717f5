import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { BusinessException } from '@common/exception/types/BusinessException';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { RecordStatus } from '@common/enums';
import { StatsDataSource, StatsFieldConfigDto } from '../dto/customer-preferences.dto';

export interface RecoveredValueSummary {
  portfolioId: string;
  portfolioName: string;
  totalRecoveredValue: number;
  itemCount: number;
}

export interface CustomerRecoveredValueSummary {
  customerId: string;
  totalRecoveredValue: number;
  portfolios: RecoveredValueSummary[];
}

@Injectable()
export class RecoveredValueUseCase {
  constructor(
    @Inject('CollectCashStatsPort')
    private readonly collectCashStatsAdapter: CollectCashStatsPort,
    @Inject('CustomerPreferencesPort')
    private readonly customerPreferencesAdapter: CustomerPreferencesPort,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
  ) { }

  async getRecoveredValuesByCustomerId(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CustomerRecoveredValueSummary> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Getting recovered values by customer ID', {
        traceId,
        customerId,
        startDate,
        endDate,
        operation: 'getRecoveredValuesByCustomerId',
        layer: 'USE_CASE',
      });

      // Get customer portfolios
      const portfolios = await this.portfolioAdapter.getAll({ customerId });

      const portfolioSummaries: RecoveredValueSummary[] = [];
      let totalRecoveredValue = 0;

      for (const portfolio of portfolios) {
        const portfolioRecoveredValue = await this.getRecoveredValuesByPortfolioId(
          portfolio.id,
          startDate,
          endDate,
        );

        if (portfolioRecoveredValue.totalRecoveredValue > 0) {
          portfolioSummaries.push(portfolioRecoveredValue);
          totalRecoveredValue += portfolioRecoveredValue.totalRecoveredValue;
        }
      }

      return {
        customerId,
        totalRecoveredValue,
        portfolios: portfolioSummaries,
      };
    } catch (error) {
      logger.error('Error getting recovered values by customer ID', {
        traceId,
        customerId,
        error: JSON.stringify(error),
        operation: 'getRecoveredValuesByCustomerId',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'getRecoveredValuesByCustomerId',
        error,
      );
    }
  }

  async getRecoveredValuesByPortfolioId(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<RecoveredValueSummary> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Getting recovered values by portfolio ID', {
        traceId,
        portfolioId,
        startDate,
        endDate,
        operation: 'getRecoveredValuesByPortfolioId',
        layer: 'USE_CASE',
      });

      const portfolio = await this.portfolioAdapter.get(portfolioId);
      const stats = await this.collectCashStatsAdapter.findByPortfolioIdWithDateRange(
        portfolioId,
        startDate,
        endDate,
      );

      const totalRecoveredValue = stats.reduce((sum, stat) => sum + stat.dealValue, 0);

      return {
        portfolioId,
        portfolioName: portfolio.name,
        totalRecoveredValue,
        itemCount: stats.length,
      };
    } catch (error) {
      logger.error('Error getting recovered values by portfolio ID', {
        traceId,
        portfolioId,
        error: JSON.stringify(error),
        operation: 'getRecoveredValuesByPortfolioId',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'getRecoveredValuesByPortfolioId',
        error,
      );
    }
  }

  async extractAndStoreRecoveredValue(
    customerId: string,
    portfolioId: string,
    portfolioItemId: string,
    workflowId: string,
  ): Promise<void> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      logger.info('Extracting and storing recovered value', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId,
        workflowId,
        operation: 'extractAndStoreRecoveredValue',
        layer: 'USE_CASE',
      });

      // Get customer preferences to determine data source
      const customerPreferences = await this.customerPreferencesAdapter.getById(customerId);

      // Try to find workflow-specific configuration first, then fall back to legacy configuration
      const recoveredValueConfig = this.getStatisticalFieldConfig(
        customerPreferences,
        workflowId,
        'recoveredValueFrom',
      );

      if (!recoveredValueConfig) {
        logger.info('No recovered value configuration found for customer and workflow', {
          traceId,
          customerId,
          workflowId,
          operation: 'extractAndStoreRecoveredValue',
          layer: 'USE_CASE',
        });
        return;
      }

      const recoveredValue = await this.extractRecoveredValue(
        portfolioItemId,
        recoveredValueConfig.source,
        recoveredValueConfig.path,
      );

      if (recoveredValue > 0) {
        // Extract all statistical fields dynamically from workflow-specific configuration
        const statisticalData = await this.extractStatisticalFields(
          customerPreferences,
          workflowId,
          portfolioItemId,
        );

        // Create collect cash stats entry with dynamically extracted values
        const collectCashStats = new CollectCashStatsEntity(
          crypto.randomUUID(),
          customerId,
          portfolioId,
          portfolioItemId,
          workflowId,
          statisticalData.dealValue || recoveredValue, // Use recoveredValue as fallback for dealValue
          statisticalData.currentDebit || 0, // Default to 0 if not configured
          statisticalData.originalDebt || 0, // Default to 0 if not configured
          1, // Default to 1 installment
          RecordStatus.ACTIVE,
        );

        await this.collectCashStatsAdapter.create(collectCashStats);

        logger.info('Recovered value stored successfully', {
          traceId,
          customerId,
          portfolioId,
          portfolioItemId,
          recoveredValue,
          operation: 'extractAndStoreRecoveredValue',
          layer: 'USE_CASE',
        });
      }
    } catch (error) {
      logger.error('Error extracting and storing recovered value', {
        traceId,
        customerId,
        portfolioId,
        portfolioItemId,
        error: JSON.stringify(error),
        operation: 'extractAndStoreRecoveredValue',
        layer: 'USE_CASE',
      });
      throw new BusinessException(
        'RecoveredValueUseCase',
        'extractAndStoreRecoveredValue',
        error,
      );
    }
  }

  private async extractRecoveredValue(
    portfolioItemId: string,
    source: StatsDataSource,
    path: string,
  ): Promise<number> {
    const traceId = CorrelationContextService.getTraceId();

    try {
      let data: any;

      const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);
      if (source === StatsDataSource.CUSTOM_DATA) {
        const customData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
          portfolioItem.customDataId,
          portfolioItemId,
        );
        data = customData?.customData;
      } else if (source === StatsDataSource.MIDDLEWARE) {
        if (portfolioItem.middlewareResponseOutputId) {
          const middlewareResponse = await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
            portfolioItem.middlewareResponseOutputId,
            portfolioItemId,
          );
          data = middlewareResponse?.data;
        }
      }

      if (!data) {
        logger.info('No data found for recovered value extraction from middleware response', {
          traceId,
          portfolioItemId,
          source,
          path,
          middlewareResponseOutputId: portfolioItem.middlewareResponseOutputId,
          operation: 'extractRecoveredValue',
          layer: 'USE_CASE',
        });
        return 0;
      }

      const value = this.getNestedProperty(data, path);
      const numericValue = this.parseNumericValue(value);

      logger.info('Recovered value extracted', {
        traceId,
        portfolioItemId,
        source,
        path,
        extractedValue: value,
        numericValue,
        operation: 'extractRecoveredValue',
        layer: 'USE_CASE',
      });

      return numericValue;
    } catch (error) {
      logger.error('Error extracting recovered value', {
        traceId,
        portfolioItemId,
        source,
        path,
        error: JSON.stringify(error),
        operation: 'extractRecoveredValue',
        layer: 'USE_CASE',
      });
      return 0;
    }
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  private parseNumericValue(value: any): number {
    if (typeof value === 'number') {
      return value;
    }

    if (typeof value === 'string') {
      // Remove common currency symbols and formatting
      const cleanValue = value
        .replace(/[R$\s]/g, '') // Remove R$, spaces
        .replace(/\./g, '') // Remove thousands separators (dots)
        .replace(',', '.'); // Replace decimal comma with dot

      const parsed = parseFloat(cleanValue);
      return isNaN(parsed) ? 0 : parsed;
    }

    return 0;
  }

  /**
   * Gets a specific statistical field configuration for a workflow
   * First tries to find workflow-specific configuration in statsConfig array
   * Falls back to legacy configuration if available and fieldName matches
   */
  private getStatisticalFieldConfig(
    customerPreferences: any,
    workflowId: string,
    fieldName: string,
  ): StatsFieldConfigDto | null {
    // Try to find workflow-specific configuration first
    if (customerPreferences?.portfolio?.statsConfig) {
      const workflowConfig = customerPreferences.portfolio.statsConfig.find(
        (config: any) => config.workflowId === workflowId,
      );

      if (workflowConfig?.[fieldName]) {
        return workflowConfig[fieldName];
      }
    }

    // Fall back to legacy configuration only for recoveredValueFrom
    if (fieldName === 'recoveredValueFrom' && customerPreferences?.portfolio?.recoveredValueFrom) {
      return customerPreferences.portfolio.recoveredValueFrom;
    }

    return null;
  }

  /**
   * Extracts all statistical fields dynamically from workflow-specific configuration
   * Maps dynamic fields to structured data for CollectCashStatsEntity creation
   */
  private async extractStatisticalFields(
    customerPreferences: any,
    workflowId: string,
    portfolioItemId: string,
  ): Promise<{
    dealValue?: number;
    currentDebit?: number;
    originalDebt?: number;
  }> {
    const traceId = CorrelationContextService.getTraceId();
    const result: { dealValue?: number; currentDebit?: number; originalDebt?: number } = {};

    try {
      logger.info('Extracting statistical fields from workflow-specific configuration', {
        traceId,
        workflowId,
        portfolioItemId,
        operation: 'extractStatisticalFields',
        layer: 'USE_CASE',
      });

      // Define statistical field mappings
      const fieldMappings = [
        { configField: 'recoveredValueFrom', resultField: 'dealValue' as const },
        { configField: 'currentDebit', resultField: 'currentDebit' as const },
        { configField: 'originalDebt', resultField: 'originalDebt' as const },
      ];

      // Extract each statistical field dynamically
      for (const { configField, resultField } of fieldMappings) {
        const fieldConfig = this.getStatisticalFieldConfig(
          customerPreferences,
          workflowId,
          configField,
        );

        if (fieldConfig) {
          const extractedValue = await this.extractRecoveredValue(
            portfolioItemId,
            fieldConfig.source,
            fieldConfig.path,
          );

          if (extractedValue > 0) {
            result[resultField] = extractedValue;

            logger.info(`Successfully extracted ${configField}`, {
              traceId,
              workflowId,
              portfolioItemId,
              configField,
              resultField,
              extractedValue,
              source: fieldConfig.source,
              path: fieldConfig.path,
              operation: 'extractStatisticalFields',
              layer: 'USE_CASE',
            });
          }
        }
      }

      logger.info('Statistical fields extraction completed', {
        traceId,
        workflowId,
        portfolioItemId,
        extractedFields: Object.keys(result),
        dealValue: result.dealValue,
        currentDebit: result.currentDebit,
        originalDebt: result.originalDebt,
        operation: 'extractStatisticalFields',
        layer: 'USE_CASE',
      });

      return result;
    } catch (error) {
      logger.error('Error during statistical fields extraction', {
        traceId,
        workflowId,
        portfolioItemId,
        error: JSON.stringify(error),
        operation: 'extractStatisticalFields',
        layer: 'USE_CASE',
      });
      return result;
    }
  }
}
