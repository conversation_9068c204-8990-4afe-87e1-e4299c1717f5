import { Inject, Injectable } from '@nestjs/common';
import { randomUUID as uuidv4 } from 'crypto';
import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { ResponsePortfolioItemDto } from '@business-base/application/dto/out/response-portfolio-item.dto';
import { PortfolioItemDto } from '@business-base/application/dto/in/portfolio-item.dto';
import { PortfolioItemEntity } from '@business-base/domain/entities/portfolio-item.entity';
import {
  GroupByDate,
  MessageType,
  PortfolioExecutionStatus,
  PortfolioItemStatus,
  RoleType,
} from '@common/enums';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { PortfolioItemExecutionHistoryUseCase } from '@business-base/application/use-cases/portfolio-item-execution-history.use-case';
import { RecoveredValueUseCase } from '@business-base/application/use-cases/recovered-value.use-case';
import { MessageHistoryResponseDto } from '@business-base/misc/interfaces/in/message-history-response.dto';
import { InfraConversationHistoryPort } from '@business-base/infrastructure/ports/http/conversation-history.port';
import { PortfolioItemWorkflowExecutionPort } from '@business-base/infrastructure/ports/db/portfolio-item-worflow-execution.port';
import { PaginatedDto } from '@common/pagination/paginated';
import { isNotEmpty } from 'class-validator';
import { ExecutePortfolioItemDto } from '@business-base/application/dto/in/execute-portfolio-item.dto';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { SQSService } from '@common/sqs/sqs.service';
import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { CustomerEntity } from '@business-base/domain/entities/customer.entity';
import { SendDirectMessageDto } from '@business-base/application/dto/out/send-direct-message.dto';
import { PortfolioEntity } from '@business-base/domain/entities/portfolio.entity';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';
import { S3Service } from '@common/s3/s3.service';
import { Readable } from 'node:stream';
import { MessageHubOutgoingMessagePort } from '@business-base/infrastructure/ports/db/message-hub-outgoing-message.port';

@Injectable()
export class PortfolioItemUseCase {
  private readonly directMessageFilesBucketName;

  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    private readonly portfolioItemExecutionHistoryUseCase: PortfolioItemExecutionHistoryUseCase,
    @Inject('InfraConversationHistoryPort')
    private readonly infraConversationHistoryAdapter: InfraConversationHistoryPort,
    @Inject('PortfolioItemWorkflowExecutionPort')
    private readonly portfolioItemWorkflowExecutionAdapter: PortfolioItemWorkflowExecutionPort,
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
    @Inject('InfraMessageHubPort')
    private readonly messageHubAdapter: InfraMessageHubPort,
    @Inject('MessageHubOutgoingMessagePort')
    private readonly messageHubOutgoingMessageAdapter: MessageHubOutgoingMessagePort,
    private readonly sqsService: SQSService,
    private readonly s3Service: S3Service,
  ) {
    this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
  }

  async create(createPortfolioItemDto: PortfolioItemDto): Promise<ResponsePortfolioItemDto> {
    logger.info(`Creating portfolio item: ${JSON.stringify(createPortfolioItemDto)}`);

    logger.info(`Creating portfolioItem with portfolioId: ${createPortfolioItemDto.portfolioId}`);
    createPortfolioItemDto.id = uuidv4();
    const protocolNumber = `${new Date().getFullYear()}${Math.floor(new Date().getTime() / 1000)}`;
    const dataAtual = new Date().toISOString();

    const customDataId = (
      await this.portfolioItemCustomDataAdapter.create({
        id: uuidv4(),
        portfolioItemId: createPortfolioItemDto.id,
        customData: {
          ...createPortfolioItemDto.customData,
          PROTOCOL_NUMBER: protocolNumber,
          DATA_ATUAL: dataAtual,
        },
      })
    ).id;

    const portfolioItem = await this.portfolioItemAdapter.create(
      this.createPortfolioItemEntity(createPortfolioItemDto, customDataId),
    );

    return this.createResponsePortfolioItemDto(portfolioItem);
  }

  async findAll(
    searchOptions: any,
    customerId: string,
    page: number,
    limit: number,
    sort: string,
  ): Promise<PaginatedDto<ResponsePortfolioItemDto>> {
    logger.info('Finding all portfolioItems');
    const sortObject = this.parseSortString(sort);
    const customerPortfolios = await this.portfolioAdapter.getAll({ customerId });
    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);
    const combinedSearchOptions = { portfolioId: { in: customerPortfoliosIds }, ...searchOptions };
    const portfolioItemsPaginated = await this.portfolioItemAdapter.findManyPaginated(
      { where: { ...combinedSearchOptions } },
      {
        page: page || 1,
        limit: limit || 10,
        sort: sortObject,
      },
    );

    const { data, ...rest } = portfolioItemsPaginated;

    return {
      items: await Promise.all(
        data.map(async item => await this.enrichPortfolioItemWithMiddlewareResponseOutput(item)),
      ),
      ...rest,
    };
  }

  async findById(portfolioItemId: string): Promise<ResponsePortfolioItemDto> {
    logger.info(`Finding portfolioItem with id: ${portfolioItemId}`);

    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      logger.error(`Portfolio item not found: ${portfolioItemId}`);
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolioItemCustomData = await this.portfolioItemCustomDataAdapter.getByPortfolioItemId(
      portfolioItem.customDataId,
      portfolioItem.id,
    );

    const portfolioItemMiddlewareResponseOutput = portfolioItem.middlewareResponseOutputId
      ? await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
        portfolioItem.middlewareResponseOutputId,
        portfolioItem.id,
      )
      : null;

    const filteredMiddlewareResponseOutput = portfolioItemMiddlewareResponseOutput
      ? this.filterShowOffData(portfolioItemMiddlewareResponseOutput.data)
      : {};

    const portfolioItemDto = this.createResponsePortfolioItemDto(
      portfolioItem,
      portfolioItemCustomData.customData,
      filteredMiddlewareResponseOutput,
    );

    logger.info(
      `Finding portfolioItem id: ${portfolioItemId}. Response: ${JSON.stringify(portfolioItemDto)}`,
    );

    return portfolioItemDto;
  }

  async updateMiddlewareResponseOutputId(
    portfolioItemId: string,
    middlewareResponseOutputId: string,
  ): Promise<void> {
    logger.info(`Updating middleware response output for item with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({ ...portfolioItem, middlewareResponseOutputId });
  }

  async delete(portfolioItemId: string): Promise<ResponsePortfolioItemDto> {
    logger.info(`Deleting portfolioItem with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    portfolioItem.currentStatus = PortfolioItemStatus.CANCELLED;

    const updatedPortfolioItemEntity = await this.portfolioItemAdapter.update(portfolioItem);

    return this.createResponsePortfolioItemDto(updatedPortfolioItemEntity);
  }

  async updateItemCurrentStatus(
    portfolioItemId: string,
    currentStatus: PortfolioItemStatus,
    reason?: string,
  ): Promise<ResponsePortfolioItemDto> {
    logger.info(`Updating portfolioItem with id: ${portfolioItemId} to ${currentStatus}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);

    if (portfolioItem.currentStatus === currentStatus) {
      logger.info(`PortfolioItem ${portfolioItemId} already in status ${currentStatus}.`);
      return this.createResponsePortfolioItemDto(portfolioItem);
    }

    const updatedPortfolioItem = {
      ...portfolioItem,
      currentStatus,
    };

    await this.portfolioItemAdapter.update(updatedPortfolioItem);

    await this.portfolioItemExecutionHistoryUseCase.create({
      portfolioItemId,
      oldStatus: portfolioItem.currentStatus,
      newStatus: updatedPortfolioItem.currentStatus,
      reason: reason,
    });

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return this.createResponsePortfolioItemDto(updatedPortfolioItem);
  }

  async updateLastInteraction(portfolioItemId: string): Promise<void> {
    const lastInteraction = new Date();
    logger.info(
      `Updating last interaction for portfolioItem with id: ${portfolioItemId} to: ${lastInteraction}`,
    );
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      lastInteraction,
    });
  }

  async updateLastMessageSentAt(portfolioItemId: string): Promise<void> {
    const lastMessageSentAt = new Date();
    logger.info(
      `Updating last interaction for portfolioItem with id: ${portfolioItemId} to: ${lastMessageSentAt}`,
    );
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      lastMessageSentAt,
    });
  }

  async updateWaitingBusinessUserResponse(
    portfolioItemId: string,
    waitingBusinessUserResponse: boolean,
  ): Promise<void> {
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    logger.info(
      `SettingWaitingBusinessUserResponse - update waitingBusinessUserResponse for portfolioItem with id: ${portfolioItemId} to: ${waitingBusinessUserResponse}`,
    );

    await this.portfolioItemAdapter.update({
      ...portfolioItem,
      waitingBusinessUserResponse,
    });
  }

  async findConversationHistoryById(portfolioItemId: string): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Finding conversation history for portfolioItem with id: ${portfolioItemId}`);
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    if (!portfolio) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `Portfolio not found for portfolioItem ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const workflowExecutionIds = (
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId: portfolioItemId })
    ).map(workflowExecutionId => workflowExecutionId.workflowExecutionId);

    const conversationHistoriesMap: MessageHistoryResponseDto[] = [];
    const customerId = portfolio.customerId;
    const to = portfolioItem.phoneNumber;

    await Promise.all(
      workflowExecutionIds.map(async workflowExecutionId => {
        const [conversationHistory, messageStatuses] = await Promise.all([
          this.infraConversationHistoryAdapter.retrieveConversationHistoryByWorkflowExecutionId(
            workflowExecutionId,
          ),
          this.messageHubOutgoingMessageAdapter.getOutgoingMessagesByCustomerId(customerId, to),
        ]);

        if (conversationHistory?.length > 0) {
          const enrichedHistory = conversationHistory.map(message => {
            const matchingStatus = messageStatuses.find(
              status => status.message === message.messageText,
            );
            return {
              ...message,
              sent: matchingStatus?.sent !== undefined ? matchingStatus.sent : undefined,
              sent_at: matchingStatus?.sent_at !== undefined ? matchingStatus.sent_at : undefined,
              time_to_go:
                matchingStatus?.time_to_go !== undefined
                  ? matchingStatus.time_to_go
                  : message.time_to_go,
            };
          });
          conversationHistoriesMap.push(...enrichedHistory);
        }
      }),
    );

    return conversationHistoriesMap;
  }

  async findAllItemsGroupedByStatus(
    portfolioId: string,
  ): Promise<Record<PortfolioItemStatus, number>> {
    logger.info(
      `Finding all portfolioItems grouped by status for portfolio with id: ${portfolioId}`,
    );

    return await this.portfolioItemAdapter.findAllGroupedByStatus(portfolioId);
  }

  async executeItem(executePortfolioItemDto: ExecutePortfolioItemDto): Promise<void> {
    logger.info(
      `Executing portfolio item to send answer message: ${JSON.stringify(executePortfolioItemDto)}`,
    );

    const newFileName = `${uuidv4()}.mp3`;

    let fileUrl: string | void;
    const filteredUrls = executePortfolioItemDto.filesUrl.filter(Boolean);
    if (executePortfolioItemDto.filesUrl && filteredUrls && filteredUrls.length > 0) {
      if (executePortfolioItemDto.filesUrl.length == 1) {
        fileUrl = executePortfolioItemDto.filesUrl[0];
      } else {
        fileUrl = await this.s3Service.concatAndUploadFilesStream(
          executePortfolioItemDto.filesUrl,
          this.directMessageFilesBucketName,
          newFileName,
        );
      }
    }
    const messageType = fileUrl ? MessageType.AUDIO : MessageType.TEXT;

    const customerPhone =
      await this.messageHubAdapter.getCustomerPhoneByPhoneNumberAndCommunicationChannel(
        executePortfolioItemDto.to,
        executePortfolioItemDto.channel,
      );

    if (!customerPhone) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const customer = await this.customerAdapter.get(customerPhone.customerId);
    logger.info(`Customer found: ${JSON.stringify(customer)}`);

    if (!customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const customerPortfolios = await this.portfolioAdapter.findAllExecutingByCustomerId(
      customer.id,
    );
    logger.info(`Customer portfolios found: ${JSON.stringify(customerPortfolios)}`);

    if (customerPortfolios.length === 0) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `Customer with phone ${executePortfolioItemDto.to} has no active portfolios`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfoliosIds = customerPortfolios.map(ports => ports.id);

    logger.info(`Portfolios ids found: ${portfoliosIds}`);

    const portfolioItems = await this.portfolioItemAdapter.findToAnswerByPortfolioIdAndPhoneNumber(
      portfoliosIds,
      executePortfolioItemDto.from,
    );

    logger.info(`Portfolio items found: ${JSON.stringify(portfolioItems)}`);
    if (portfolioItems.length === 0) {
      const defaultPortfolio = await this.portfolioAdapter.findDefaultByCustomerId(customer.id);
      if (!defaultPortfolio) {
        logger.info(
          `Discarding message From: ${executePortfolioItemDto.from}: ${executePortfolioItemDto.message}, No default portfolio found for customer with id: ${customer.id}.`,
        );
        return;
      }

      const newPortfolioItemDto: PortfolioItemDto = {
        id: uuidv4(),
        portfolioId: defaultPortfolio.id,
        phoneNumber: executePortfolioItemDto.from,
        customData: {
          phoneNumber: executePortfolioItemDto.from,
          channel: executePortfolioItemDto.channel,
          firstMessage: executePortfolioItemDto.message,
          fileUrl: fileUrl,
          startDate: new Date(),
          fellIntoNet: true,
        },
        line: defaultPortfolio.totalQuantity + 1,
      };

      const newPortfolioItem = await this.create(newPortfolioItemDto);
      await this.portfolioAdapter.updateTotalQuantity(
        defaultPortfolio.id,
        defaultPortfolio.totalQuantity + 1,
      );
      await this.executeItemInbound(newPortfolioItem, customer, messageType, fileUrl);
      return;
    }

    const portfolioItemPortfoliosMap = new Map(
      portfolioItems.map(portfolioItem => {
        const portfolio = customerPortfolios.find(
          portfolio => portfolio.id === portfolioItem.portfolioId,
        );
        return [portfolioItem, portfolio];
      }),
    );

    const [selectedPortfolioItem, selectedPortfolio] = await this.selectItemToAnswer(
      portfolioItemPortfoliosMap,
    );

    if (!selectedPortfolioItem) {
      logger.info(
        `Discarding message from ${executePortfolioItemDto.from} to ${executePortfolioItemDto.to}, message: ${executePortfolioItemDto.message}. Item was not found to answer.`,
      );
      return;
    }

    await this.updateLastInteraction(selectedPortfolioItem.id);

    const portfolioItem = this.createResponsePortfolioItemDto(selectedPortfolioItem);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.PENDING &&
      selectedPortfolio.executionStatus === PortfolioExecutionStatus.INBOUND
    ) {
      await this.executeItemInbound(portfolioItem, customer, messageType, fileUrl);
      return;
    }

    const itemWorkflowExecutions = await this.portfolioItemWorkflowExecutionAdapter.getAll({
      portfolioItemId: portfolioItem.id,
    });

    logger.info(`Item workflow executions found: ${JSON.stringify(itemWorkflowExecutions)}`);

    if (itemWorkflowExecutions.length === 0) {
      throw new BusinessException(
        'Portfolio-item-use-case: executeItem',
        `No workflow executions found for portfolio item ${portfolioItem.id}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const workflowExecutionId = itemWorkflowExecutions[0].workflowExecutionId;
    const workflowId = customerPortfolios.find(
      portfolio => portfolio.id === portfolioItem.portfolioId,
    ).workflowId;

    logger.info(`Workflow id found: ${workflowId}`);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.UNLINKED ||
      portfolioItem.currentStatus === PortfolioItemStatus.SUCCEED ||
      portfolioItem.currentStatus === PortfolioItemStatus.FAILED ||
      portfolioItem.currentStatus === PortfolioItemStatus.OPTED_OUT ||
      portfolioItem.currentStatus === PortfolioItemStatus.FINISHED
    ) {
      logger.info(`Message history updating: ${workflowExecutionId}`);

      await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
        workflowExecutionId,
        new SendDirectMessageDto(
          executePortfolioItemDto.message ? executePortfolioItemDto.message : ' ',
          executePortfolioItemDto.messageType,
          RoleType.USER,
          fileUrl,
        ),
      );

      logger.info(
        `PortfolioItem ${portfolioItem.id} will not be executed because its status ${portfolioItem.currentStatus}.`,
      );

      await this.updateWaitingBusinessUserResponse(portfolioItem.id, true);

      return;
    }

    const message = executePortfolioItemDto.message;

    const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);
    const messageBody = {
      portfolioItem,
      workflowId,
      workflowExecutionId,
      message,
      messageType: fileUrl ? MessageType.AUDIO : MessageType.TEXT,
      fileUrl,
      isFirstMessage: false,
    };

    await this.sqsService.produce(process.env[queueUrl], messageBody);
  }

  async executeItemInbound(
    portfolioItemDto: ResponsePortfolioItemDto,
    customer: CustomerEntity,
    messageType: MessageType,
    fileUrl: string | void,
  ): Promise<void> {
    logger.info(
      `Executing item for inbound message: ${JSON.stringify(portfolioItemDto)}. Customer id: ${customer.id
      }`,
    );

    const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);
    const messageBody = {
      portfolioId: portfolioItemDto.portfolioId,
      portfolioItemId: portfolioItemDto.id,
      messageType,
      isFirstMessage: true,
      fileUrl,
    };

    await this.sqsService.produce(process.env[queueUrl], messageBody);
  }

  async sendDirectMessage(
    portfolioItemId: string,
    sendDirectMessageDto: SendDirectMessageDto,
    file?: Express.Multer.File,
  ): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Sending direct message to portfolio item with id: ${portfolioItemId}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);
    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    const customer = await this.customerAdapter.get(portfolio.customerId);

    if (!portfolioItem || !portfolio || !customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `Customer or Portfolio or PortfolioItem not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const [portfolioItemWorkflowExecution] =
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId });

    if (!portfolioItemWorkflowExecution) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `No workflow execution found for portfolio item ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    let modifiedSendDirectMessageDto = sendDirectMessageDto;
    if (file && sendDirectMessageDto.messageType === MessageType.PDF) {
      const fileRenamed = this.getFileRenamed(file);
      const s3Url = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileRenamed,
        file.buffer,
      );

      if (!s3Url) {
        throw new BusinessException(
          'Portfolio-item-use-case: sendDirectMessage',
          `Error uploading file: ${file.originalname} to S3`,
          BusinessExceptionStatus.GENERAL_ERROR,
        );
      }
      modifiedSendDirectMessageDto = { ...sendDirectMessageDto, fileUrl: s3Url };
    }

    const messagesHistory =
      await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
        portfolioItemWorkflowExecution.workflowExecutionId,
        modifiedSendDirectMessageDto,
      );

    await this.messageHubAdapter.sendMessage({
      customerId: customer.id,
      to: portfolioItem.phoneNumber,
      messageType: sendDirectMessageDto.messageType,
      communicationChannel: portfolio.communicationChannel,
      message: sendDirectMessageDto.message,
      isFirstMessage: false,
      fileUrl: modifiedSendDirectMessageDto.fileUrl,
    });

    await this.updateLastMessageSentAt(portfolioItem.id);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS &&
      sendDirectMessageDto.roleType === RoleType.ATTENDANT
    ) {
      await this.updateItemCurrentStatus(portfolioItemId, PortfolioItemStatus.UNLINKED);
    }

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return messagesHistory;
  }

  async sendDirectMessageBase64(
    portfolioItemId: string,
    sendDirectMessageDto: SendDirectMessageDto,
    arquivoBase64: string,
    fileName: string,
  ): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Sending direct message to portfolio item with id: ${portfolioItemId}`);
    const portfolioItem = await this.getPortfolioItemEntity(portfolioItemId);
    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);
    const customer = await this.customerAdapter.get(portfolio.customerId);

    if (!portfolioItem || !portfolio || !customer) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `Customer or Portfolio or PortfolioItem not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const [portfolioItemWorkflowExecution] =
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId });

    if (!portfolioItemWorkflowExecution) {
      throw new BusinessException(
        'Portfolio-item-use-case: sendDirectMessage',
        `No workflow execution found for portfolio item ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const buffer = Buffer.from(arquivoBase64, 'base64');

    let modifiedSendDirectMessageDto = sendDirectMessageDto;
    if (buffer && sendDirectMessageDto.messageType === MessageType.PDF) {
      const fileRenamed = this.getFileRenamedFromString(fileName);
      const s3Url = await this.s3Service.uploadFile(
        this.directMessageFilesBucketName,
        fileRenamed,
        buffer,
      );

      if (!s3Url) {
        throw new BusinessException(
          'Portfolio-item-use-case: sendDirectMessageBase64',
          `Error uploading file: ${fileRenamed} to S3`,
          BusinessExceptionStatus.GENERAL_ERROR,
        );
      }
      modifiedSendDirectMessageDto = { ...sendDirectMessageDto, fileUrl: s3Url };
    }

    const messagesHistory =
      await this.infraConversationHistoryAdapter.createConversationHistoryByWorkflowExecutionId(
        portfolioItemWorkflowExecution.workflowExecutionId,
        modifiedSendDirectMessageDto,
      );

    await this.messageHubAdapter.sendMessage({
      customerId: customer.id,
      to: portfolioItem.phoneNumber,
      messageType: sendDirectMessageDto.messageType,
      communicationChannel: portfolio.communicationChannel,
      message: sendDirectMessageDto.message,
      isFirstMessage: false,
      fileUrl: modifiedSendDirectMessageDto.fileUrl,
    });

    await this.updateLastMessageSentAt(portfolioItem.id);

    if (
      portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS &&
      sendDirectMessageDto.roleType === RoleType.USER
    ) {
      await this.updateItemCurrentStatus(portfolioItemId, PortfolioItemStatus.UNLINKED);
    }

    await this.updateWaitingBusinessUserResponse(portfolioItemId, false);

    return messagesHistory;
  }

  async getPortfolioItemsCountByDateFilteredByStatus(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
    groupByDate: GroupByDate,
    currentStatus: PortfolioItemStatus,
  ): Promise<any> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId,
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.getAll({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
      currentStatus: currentStatus,
    });

    if (groupByDate === GroupByDate.DAY) {
      return portfolioItems.reduce((acc, item) => {
        const date = item.updatedAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});
    }

    if (groupByDate === GroupByDate.MONTH) {
      return portfolioItems.reduce((acc, item) => {
        const date = new Date(item.updatedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        acc[monthKey] = (acc[monthKey] || 0) + 1;
        return acc;
      }, {});
    }
  }

  async getPortfolioItemsWithAiOnlyInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<number> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    return await this.portfolioItemAdapter.findPortfolioItemsWithAiOnlyInteractionCountByDate(
      customerId,
      dateStart,
      dateEnd,
    );
  }

  async getPortfolioItemsWithInteractionCountByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
    groupByDate: GroupByDate,
  ): Promise<any> {
    dateStart = new Date(dateStart);
    dateStart.setUTCHours(0, 0, 0, 0);

    dateEnd = new Date(dateEnd);
    dateEnd.setUTCHours(23, 59, 59, 999);

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId,
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.getAll({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
      lastInteraction: {
        not: null,
      },
    });

    if (groupByDate === GroupByDate.DAY) {
      return portfolioItems.reduce((acc, item) => {
        const date = item.updatedAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {});
    }

    if (groupByDate === GroupByDate.MONTH) {
      return portfolioItems.reduce((acc, item) => {
        const date = new Date(item.updatedAt);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-01`;
        acc[monthKey] = (acc[monthKey] || 0) + 1;
        return acc;
      }, {});
    }
  }

  private getFileRenamed(file: Express.Multer.File) {
    return file.originalname.replace('.pdf', '') + Math.floor(new Date().getTime() / 1000) + '.pdf';
  }

  private getFileRenamedFromString(fileName: string) {
    return fileName.replace('.pdf', '') + Math.floor(new Date().getTime() / 1000) + '.pdf';
  }

  async getDirectMessageFileStream(fileKey: string): Promise<Readable> {
    const fileStream = await this.s3Service.getFileStream(
      this.directMessageFilesBucketName,
      fileKey,
    );

    if (!fileStream) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        ` File: ${fileKey} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return fileStream;
  }

  async getDirectMessageFileStreamFromPath(filePath: string): Promise<Readable> {
    const fileStream = await this.s3Service.getFileStreamFromPath(filePath);

    if (!fileStream) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        ` File: ${filePath} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return fileStream;
  }

  private async enrichPortfolioItemWithMiddlewareResponseOutput(
    portfolioItem: PortfolioItemEntity,
  ) {
    if (portfolioItem.middlewareResponseOutputId) {
      const middlewareResponseOutput =
        await this.middlewareResponseOutputAdapter.getByPortfolioItemId(
          portfolioItem.middlewareResponseOutputId,
          portfolioItem.id,
        );

      const filteredData = this.filterShowOffData(middlewareResponseOutput.data);

      return this.createResponsePortfolioItemDto(portfolioItem, null, filteredData);
    }

    return this.createResponsePortfolioItemDto(portfolioItem);
  }

  private createResponsePortfolioItemDto(
    portfolioItemEntity: PortfolioItemEntity,
    customData: any = null,
    middlewareResponseOutput: any = null,
  ): ResponsePortfolioItemDto {
    return new ResponsePortfolioItemDto(
      portfolioItemEntity.id,
      portfolioItemEntity.portfolioId,
      portfolioItemEntity.phoneNumber,
      portfolioItemEntity.customDataId,
      portfolioItemEntity.line,
      customData,
      portfolioItemEntity.middlewareResponseOutputId,
      middlewareResponseOutput,
      portfolioItemEntity.lastInteraction,
      portfolioItemEntity.lastMessageSentAt,
      portfolioItemEntity.lastFollowUpAt,
      portfolioItemEntity.followUpCount,
      portfolioItemEntity.waitingBusinessUserResponse,
      portfolioItemEntity.currentStatus,
      portfolioItemEntity.createdAt,
      portfolioItemEntity.updatedAt,
    );
  }

  private createPortfolioItemEntity(
    portfolioItemDto: PortfolioItemDto,
    customDataId: string,
  ): PortfolioItemEntity {
    return new PortfolioItemEntity(
      portfolioItemDto.id,
      portfolioItemDto.portfolioId,
      portfolioItemDto.phoneNumber,
      customDataId,
      portfolioItemDto.line,
      null,
      null,
      null,
      null,
      PortfolioItemStatus.PENDING,
      0,
      portfolioItemDto.createdAt,
      portfolioItemDto.updatedAt,
    );
  }

  private async getPortfolioItemEntity(portfolioItemId: string): Promise<PortfolioItemEntity> {
    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return portfolioItem;
  }

  /**
   Prioridade 1: assumimos o in_progress ou followed_up
   Prioridade 2: escolher o item pending se o portfolio for execute immediatelly == false
   Prioridade 3: escolher o item com base na last intereaction mais recente
   */
  private async selectItemToAnswer(
    portfolioItemsPortfolioMap: Map<PortfolioItemEntity, PortfolioEntity>,
  ): Promise<[PortfolioItemEntity, PortfolioEntity]> {
    for (const [portfolioItem, portfolio] of portfolioItemsPortfolioMap.entries()) {
      if (
        portfolioItem.currentStatus === PortfolioItemStatus.IN_PROGRESS ||
        portfolioItem.currentStatus === PortfolioItemStatus.FOLLOWED_UP
      ) {
        return [portfolioItem, portfolio];
      } else if (
        portfolioItem.currentStatus === PortfolioItemStatus.PENDING &&
        portfolio.executionStatus === PortfolioExecutionStatus.INBOUND
      ) {
        return [portfolioItem, portfolio];
      }
    }

    if (portfolioItemsPortfolioMap.size <= 0) {
      return null;
    }

    const sortedPortfolioItems = Array.from(portfolioItemsPortfolioMap.keys()).sort(
      (a, b) => (b.lastInteraction?.getTime() || 0) - (a.lastInteraction?.getTime() || 0),
    );

    return [
      sortedPortfolioItems[sortedPortfolioItems.length - 1],
      portfolioItemsPortfolioMap.get(sortedPortfolioItems[sortedPortfolioItems.length - 1]),
    ];
  }

  private filterShowOffData(data: any): any {
    return Object.fromEntries(
      Object.entries(data)
        .filter(([_, value]) => (value as { showOff?: boolean }).showOff === true)
        .map(([key, value]) => {
          const { showOff: _showOff, ...rest } = value as { showOff?: boolean;[key: string]: any };
          return [key, rest];
        }),
    );
  }

  private parseSortString(sortString: string): Record<string, 'asc' | 'desc'>[] {
    if (sortString && isNotEmpty(sortString)) {
      try {
        return JSON.parse(sortString);
      } catch (error) {
        throw new BusinessException(
          'Pagination',
          'Invalid sort string format. Expected a format like "{lineNumber: \'asc\'}"',
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }
    }
  }

  async getCustomerIdByPortfolioItemId(portfolioItemId: string): Promise<string> {
    logger.info(`Getting customerId for portfolioItemId: ${portfolioItemId}`);

    const portfolioItem = await this.portfolioItemAdapter.get(portfolioItemId);

    if (!portfolioItem) {
      logger.error(`Portfolio item not found: ${portfolioItemId}`);
      throw new BusinessException(
        'Portfolio-item-use-case',
        `PortfolioItem with id ${portfolioItemId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const portfolio = await this.portfolioAdapter.get(portfolioItem.portfolioId);

    if (!portfolio) {
      logger.error(`Portfolio not found for portfolioItem: ${portfolioItemId}`);
      throw new BusinessException(
        'Portfolio-item-use-case',
        `Portfolio not found for portfolioItem ${portfolioItemId}`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return portfolio.customerId;
  }
}
