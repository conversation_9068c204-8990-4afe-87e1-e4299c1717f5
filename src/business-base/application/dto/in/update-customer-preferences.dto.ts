import {
  IsUUID,
  IsString,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsObject,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { RecordStatus } from '@common/enums';
export class UpdateTaxRulesDto {
  @ApiProperty({
    description: 'Penalty fee percentage (e.g., "2.69" for 2.69%)',
    example: '2.69',
    required: false,
  })
  @IsString({ message: 'penaltyFee must be a string' })
  @IsOptional()
  readonly penaltyFee?: string;

  @ApiProperty({
    description: 'Daily fee percentage (e.g., "0,0003333" for 0.0003333%)',
    example: '0,0003333',
    required: false,
  })
  @IsString({ message: 'dailyFee must be a string' })
  @IsOptional()
  readonly dailyFee?: string;

  constructor(penaltyFee?: string, dailyFee?: string) {
    this.penaltyFee = penaltyFee;
    this.dailyFee = dailyFee;
  }
}

export class UpdateCustomImportConfigDto {
  @ApiProperty({
    description: 'Custom CSV delimiter for portfolio import files',
    example: '|',
    required: false,
  })
  @IsString({ message: 'delimiter must be a string' })
  @IsOptional()
  readonly delimiter?: string;

  @ApiProperty({
    description: 'Tax rules configuration for business calculations',
    type: UpdateTaxRulesDto,
    required: false,
  })
  @ValidateNested({ message: 'taxRules must be valid' })
  @Type(() => UpdateTaxRulesDto)
  @IsOptional()
  readonly taxRules?: UpdateTaxRulesDto;

  @ApiProperty({
    description: 'Header mapping for CSV column transformation',
    example: { 'valor da >>> dívida$@': 'REFERENCIA_DA_DIVIDA' },
    required: false,
  })
  @IsObject({ message: 'headerMapping must be an object' })
  @IsOptional()
  readonly headerMapping?: Record<string, string>;

  @ApiProperty({
    description: 'Post-processing headers for CSV files',
    example: ['status', 'lastInteraction', 'followUpCount'],
    type: [String],
    required: false,
  })
  @IsArray({ message: 'additionalHeaders must be an array' })
  @IsString({ each: true, message: 'Each post-processing header must be a string' })
  @IsOptional()
  readonly additionalHeaders?: string[];

  constructor(
    delimiter?: string,
    taxRules?: UpdateTaxRulesDto,
    headerMapping?: Record<string, string>,
    additionalHeaders?: string[],
  ) {
    this.delimiter = delimiter;
    this.taxRules = taxRules;
    this.headerMapping = headerMapping;
    this.additionalHeaders = additionalHeaders;
  }
}
export class UpdatePortfolioPreferencesDto {
  @ApiProperty({
    description: 'UUID of the default workflow to be used for portfolio processing',
    example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
    format: 'uuid',
    required: false,
  })
  @IsUUID('4', { message: 'defaultWorkflowId must be a valid UUID' })
  @IsOptional()
  readonly defaultWorkflowId?: string;

  @ApiProperty({
    description: 'Timezone offset in UTC format (e.g., "-3" for UTC-3, "+5.5" for UTC+5:30)',
    example: '-3',
    pattern: '^[+-]?\\d+(\\.\\d+)?$',
    required: false,
  })
  @IsString({ message: 'timezoneUTC must be a string' })
  @IsOptional()
  @Matches(/^[+-]?\d+(\.\d+)?$/, {
    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
  })
  readonly timezoneUTC?: string;

  @ApiProperty({
    description:
      'Cron expression for portfolio import scheduling (5 fields: minute hour day month weekday)',
    example: '0 9 * * 1-5',
    pattern:
      '^(\\*|\\*\\/[1-9]\\d*|[0-5]?\\d|[0-5]?\\d-[0-5]?\\d|[0-5]?\\d(,[0-5]?\\d)*) (\\*|\\*\\/[1-9]\\d*|[01]?\\d|2[0-3]|[01]?\\d-[01]?\\d|2[0-3]-2[0-3]|[01]?\\d(,[01]?\\d)*|2[0-3](,2[0-3])*) (\\*|\\*\\/[1-9]\\d*|[12]?\\d|3[01]|[12]?\\d-[12]?\\d|3[01]-3[01]|[12]?\\d(,[12]?\\d)*|3[01](,3[01])*) (\\*|\\*\\/[1-9]\\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\\*|\\*\\/[1-9]\\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$',
    required: false,
  })
  @IsString({ message: 'importCronExpression must be a string' })
  @IsOptional()
  @Matches(
    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
    {
      message: 'importCronExpression must be a valid cron expression',
    },
  )
  readonly importCronExpression?: string;

  @ApiProperty({
    description: 'UUID of the workflow to be used for follow-up processing',
    example: '7f413811-4aa8-43f4-8c48-d00143dd226e',
    format: 'uuid',
    required: false,
  })
  @IsUUID('4', { message: 'followUpWorkflowId must be a valid UUID' })
  @IsOptional()
  readonly followUpWorkflowId?: string;

  @ApiProperty({
    description:
      'Cron expression for follow-up scheduling (5 fields: minute hour day month weekday)',
    example: '0 */2 * * *',
    pattern:
      '^(\\*|\\*\\/[1-9]\\d*|[0-5]?\\d|[0-5]?\\d-[0-5]?\\d|[0-5]?\\d(,[0-5]?\\d)*) (\\*|\\*\\/[1-9]\\d*|[01]?\\d|2[0-3]|[01]?\\d-[01]?\\d|2[0-3]-2[0-3]|[01]?\\d(,[01]?\\d)*|2[0-3](,2[0-3])*) (\\*|\\*\\/[1-9]\\d*|[12]?\\d|3[01]|[12]?\\d-[12]?\\d|3[01]-3[01]|[12]?\\d(,[12]?\\d)*|3[01](,3[01])*) (\\*|\\*\\/[1-9]\\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\\*|\\*\\/[1-9]\\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$',
    required: false,
  })
  @IsString({ message: 'followUpCronExpression must be a string' })
  @IsOptional()
  @Matches(
    /^(\*|\*\/[1-9]\d*|[0-5]?\d|[0-5]?\d-[0-5]?\d|[0-5]?\d(,[0-5]?\d)*) (\*|\*\/[1-9]\d*|[01]?\d|2[0-3]|[01]?\d-[01]?\d|2[0-3]-2[0-3]|[01]?\d(,[01]?\d)*|2[0-3](,2[0-3])*) (\*|\*\/[1-9]\d*|[12]?\d|3[01]|[12]?\d-[12]?\d|3[01]-3[01]|[12]?\d(,[12]?\d)*|3[01](,3[01])*) (\*|\*\/[1-9]\d*|[1-9]|1[0-2]|[1-9]-[1-9]|1[0-2]-1[0-2]|[1-9](,[1-9])*|1[0-2](,1[0-2])*) (\*|\*\/[1-9]\d*|[0-6]|[0-6]-[0-6]|[0-6](,[0-6])*)$/,
    {
      message: 'followUpCronExpression must be a valid cron expression',
    },
  )
  readonly followUpCronExpression?: string;

  @ApiProperty({
    description: 'Maximum number of follow-up attempts for each portfolio item',
    example: 3,
    minimum: 1,
    required: false,
  })
  @IsNumber({}, { message: 'followUpQuantity must be a number' })
  @IsPositive({ message: 'followUpQuantity must be a positive number' })
  @IsOptional()
  readonly followUpQuantity?: number;

  @ApiProperty({
    description: 'Interval in minutes between follow-up attempts',
    example: 120,
    minimum: 1,
    required: false,
  })
  @IsNumber({}, { message: 'followUpIntervalMinutes must be a number' })
  @IsPositive({ message: 'followUpIntervalMinutes must be a positive number' })
  @IsOptional()
  readonly followUpIntervalMinutes?: number;

  @ApiProperty({
    description: 'Array of column names to include in portfolio exports',
    example: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
    type: [String],
    required: false,
  })
  @IsArray({ message: 'exportColumns must be an array' })
  @IsString({ each: true, message: 'Each export column must be a string' })
  @IsOptional()
  readonly exportColumns?: string[];

  @ApiProperty({
    description: 'Custom CSV delimiter for portfolio import files (default: ",")',
    example: '|',
    required: false,
  })
  @IsString({ message: 'delimiter must be a string' })
  @IsOptional()
  readonly delimiter?: string;

  @ApiProperty({
    description: 'Custom import configuration with YAML-based structure',
    type: UpdateCustomImportConfigDto,
    required: false,
  })
  @ValidateNested({ message: 'customImportConfig must be valid' })
  @Type(() => UpdateCustomImportConfigDto)
  @IsOptional()
  readonly customImportConfig?: UpdateCustomImportConfigDto;

  constructor(
    defaultWorkflowId?: string,
    timezoneUTC?: string,
    importCronExpression?: string,
    followUpWorkflowId?: string,
    followUpCronExpression?: string,
    followUpQuantity?: number,
    followUpIntervalMinutes?: number,
    exportColumns?: string[],
    customImportConfig?: UpdateCustomImportConfigDto,
  ) {
    this.defaultWorkflowId = defaultWorkflowId;
    this.timezoneUTC = timezoneUTC;
    this.importCronExpression = importCronExpression;
    this.followUpWorkflowId = followUpWorkflowId;
    this.followUpCronExpression = followUpCronExpression;
    this.followUpQuantity = followUpQuantity;
    this.followUpIntervalMinutes = followUpIntervalMinutes;
    this.exportColumns = exportColumns;
    this.customImportConfig = customImportConfig;
  }
}

export class UpdateCustomerPreferencesDto {
  @ApiProperty({
    description: 'Portfolio-specific preferences and configurations to update',
    type: UpdatePortfolioPreferencesDto,
    required: false,
  })
  @ValidateNested({ message: 'portfolio preferences must be valid' })
  @Type(() => UpdatePortfolioPreferencesDto)
  @IsOptional()
  readonly portfolio?: UpdatePortfolioPreferencesDto;

  @IsEnum(RecordStatus, { message: 'status must be a valid RecordStatus' })
  @IsOptional()
  readonly status?: RecordStatus;

  constructor(data?: Partial<UpdateCustomerPreferencesDto>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
