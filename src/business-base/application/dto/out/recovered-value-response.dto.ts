import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PortfolioRecoveredValueDto {
  @ApiProperty({
    description: 'UUID of the portfolio',
    example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
    format: 'uuid',
  })
  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @ApiProperty({
    description: 'Name of the portfolio',
    example: 'Portfolio Janeiro 2024',
  })
  @IsString()
  @IsNotEmpty()
  readonly portfolioName: string;

  @ApiProperty({
    description: 'Total recovered value for this portfolio',
    example: 15750.50,
    type: 'number',
    format: 'float',
  })
  @IsNumber()
  @IsNotEmpty()
  readonly totalRecoveredValue: number;

  @ApiProperty({
    description: 'Number of portfolio items with recovered values',
    example: 25,
    type: 'integer',
  })
  @IsNumber()
  @IsNotEmpty()
  readonly itemCount: number;

  constructor(
    portfolioId: string,
    portfolioName: string,
    totalRecoveredValue: number,
    itemCount: number,
  ) {
    this.portfolioId = portfolioId;
    this.portfolioName = portfolioName;
    this.totalRecoveredValue = totalRecoveredValue;
    this.itemCount = itemCount;
  }
}

export class CustomerRecoveredValueResponseDto {
  @ApiProperty({
    description: 'UUID of the customer',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
  })
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @ApiProperty({
    description: 'Total recovered value across all portfolios',
    example: 45250.75,
    type: 'number',
    format: 'float',
  })
  @IsNumber()
  @IsNotEmpty()
  readonly totalRecoveredValue: number;

  @ApiProperty({
    description: 'Recovered value breakdown by portfolio',
    type: [PortfolioRecoveredValueDto],
    isArray: true,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PortfolioRecoveredValueDto)
  readonly portfolios: PortfolioRecoveredValueDto[];

  constructor(
    customerId: string,
    totalRecoveredValue: number,
    portfolios: PortfolioRecoveredValueDto[],
  ) {
    this.customerId = customerId;
    this.totalRecoveredValue = totalRecoveredValue;
    this.portfolios = portfolios;
  }
}

export class RecoveredValueQueryDto {
  @ApiProperty({
    description: 'Start date for filtering recovered values (ISO 8601 format)',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  readonly startDate?: string;

  @ApiProperty({
    description: 'End date for filtering recovered values (ISO 8601 format)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  readonly endDate?: string;

  constructor(startDate?: string, endDate?: string) {
    this.startDate = startDate;
    this.endDate = endDate;
  }
}
