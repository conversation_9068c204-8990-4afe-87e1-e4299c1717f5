import {
  IsUUID,
  IsString,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsObject,
  IsEnum,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { RecordStatus } from '@common/enums';

export enum StatsDataSource {
  CUSTOM_DATA = 'customData',
  MIDDLEWARE = 'middleware',
}

export class StatsFieldConfigDto {
  @ApiProperty({
    description: 'Source of the data (customData or middleware)',
    enum: StatsDataSource,
    example: StatsDataSource.MIDDLEWARE,
    required: true,
  })
  @IsEnum(StatsDataSource, { message: 'source must be either customData or middleware' })
  @IsNotEmpty()
  readonly source: StatsDataSource;

  @ApiProperty({
    description: 'Path to the field in the data source (e.g., "VALOR_RECEBIDO" for customData or "[\'deal-info\'].VALOR_RECUPERADO" for middleware)',
    example: '[\'deal-info\'].VALOR_RECUPERADO',
    required: true,
  })
  @IsString({ message: 'path must be a string' })
  @IsNotEmpty()
  readonly path: string;

  constructor(source: StatsDataSource, path: string) {
    this.source = source;
    this.path = path;
  }
}

export class StatsConfigDto {
  @ApiProperty({
    description: 'UUID of the workflow this configuration applies to',
    example: 'matrix-energia-negociador-divida-v1',
    required: true,
  })
  @IsString({ message: 'workflowId must be a string' })
  @IsNotEmpty()
  readonly workflowId: string;

  @ApiProperty({
    description: 'Configuration for recovered value extraction',
    type: StatsFieldConfigDto,
    required: false,
    example: {
      source: 'middleware',
      path: '[\'deal-info\'].VALOR_RECUPERADO',
    },
  })
  @ValidateNested({ message: 'recoveredValueFrom must be valid' })
  @Type(() => StatsFieldConfigDto)
  @IsOptional()
  readonly recoveredValueFrom?: StatsFieldConfigDto;

  @ApiProperty({
    description: 'Configuration for current debt extraction',
    type: StatsFieldConfigDto,
    required: false,
    example: {
      source: 'customData',
      path: 'VALOR_CORRIGIDO',
    },
  })
  @ValidateNested({ message: 'currentDebit must be valid' })
  @Type(() => StatsFieldConfigDto)
  @IsOptional()
  readonly currentDebit?: StatsFieldConfigDto;

  // Allow additional dynamic properties while maintaining type safety
  [key: string]: any;

  constructor(
    workflowId: string,
    ...additionalFields: StatsFieldConfigDto[]
  ) {
    this.workflowId = workflowId;

    // Handle additional dynamic fields
    if (additionalFields && additionalFields.length > 0) {
      Object.assign(this, additionalFields[0]);
    }
  }
}

export class TaxRulesDto {
  @ApiProperty({
    description: 'Penalty fee percentage (e.g., "2.69" for 2.69%)',
    example: '2.69',
    required: false,
  })
  @IsString({ message: 'penaltyFee must be a string' })
  @IsOptional()
  readonly penaltyFee?: string;

  @ApiProperty({
    description: 'Daily fee percentage (e.g., "0,0003333" for 0.0003333%)',
    example: '0,0003333',
    required: false,
  })
  @IsString({ message: 'dailyFee must be a string' })
  @IsOptional()
  readonly dailyFee?: string;

  constructor(penaltyFee?: string, dailyFee?: string) {
    this.penaltyFee = penaltyFee;
    this.dailyFee = dailyFee;
  }
}

export class CustomImportConfigDto {
  @ApiProperty({
    description: 'Custom CSV delimiter for portfolio import files',
    example: '|',
    required: false,
  })
  @IsString({ message: 'delimiter must be a string' })
  @IsOptional()
  readonly delimiter?: string;

  @ApiProperty({
    description: 'Tax rules configuration for business calculations',
    type: TaxRulesDto,
    required: false,
  })
  @ValidateNested({ message: 'taxRules must be valid' })
  @Type(() => TaxRulesDto)
  @IsOptional()
  readonly taxRules?: TaxRulesDto;

  @ApiProperty({
    description: 'Header mapping for CSV column transformation',
    example: { 'valor da >>> dívida$@': 'REFERENCIA_DA_DIVIDA' },
    required: false,
  })
  @IsObject({ message: 'headerMapping must be an object' })
  @IsOptional()
  readonly headerMapping?: Record<string, string>;

  @ApiProperty({
    description: 'Post-processing headers for additional transformations',
    example: ['VALOR_DIVIDA_CORRIGIDO'],
    type: [String],
    required: false,
  })
  @IsArray({ message: 'additionalHeaders must be an array' })
  @IsString({ each: true, message: 'Each post-processing header must be a string' })
  @IsOptional()
  readonly additionalHeaders?: string[];

  constructor(
    delimiter?: string,
    taxRules?: TaxRulesDto,
    headerMapping?: Record<string, string>,
    additionalHeaders?: string[],
  ) {
    this.delimiter = delimiter;
    this.taxRules = taxRules;
    this.headerMapping = headerMapping;
    this.additionalHeaders = additionalHeaders;
  }
}

export class PortfolioPreferencesDto {
  @ApiProperty({
    description: 'UUID of the default workflow to be used for portfolio processing',
    example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
    format: 'uuid',
    required: false,
  })
  @IsUUID('4', { message: 'defaultWorkflowId must be a valid UUID' })
  @IsOptional()
  readonly defaultWorkflowId?: string;

  @ApiProperty({
    description: 'Timezone offset in UTC format (e.g., "-3" for UTC-3, "+5.5" for UTC+5:30)',
    example: '-3',
    pattern: '^[+-]?\\d+(\\.\\d+)?$',
    required: false,
  })
  @IsString({ message: 'timezoneUTC must be a string' })
  @Matches(/^[+-]?\d+(\.\d+)?$/, {
    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
  })
  @IsOptional()
  readonly timezoneUTC?: string;

  @ApiProperty({
    description:
      'Cron expression for portfolio import scheduling (5 fields: minute hour day month weekday)',
    example: '0 9 * * 1-5',
    required: false,
  })
  @IsString({ message: 'importCronExpression must be a string' })
  @IsOptional()
  readonly importCronExpression?: string;

  @ApiProperty({
    description: 'UUID of the workflow to be used for follow-up processing',
    example: '7f413811-4aa8-43f4-8c48-d00143dd226e',
    format: 'uuid',
    required: false,
  })
  @IsUUID('4', { message: 'followUpWorkflowId must be a valid UUID' })
  @IsOptional()
  readonly followUpWorkflowId?: string;

  @ApiProperty({
    description:
      'Cron expression for follow-up scheduling (5 fields: minute hour day month weekday)',
    required: false,
  })
  @IsString({ message: 'followUpCronExpression must be a string' })
  @IsOptional()
  readonly followUpCronExpression?: string;

  @ApiProperty({
    description: 'Maximum number of follow-up attempts for each portfolio item',
    example: 3,
    minimum: 1,
    required: false,
  })
  @IsNumber({}, { message: 'followUpQuantity must be a number' })
  @IsPositive({ message: 'followUpQuantity must be a positive number' })
  @IsOptional()
  readonly followUpQuantity?: number;

  @ApiProperty({
    description: 'Interval in minutes between follow-up attempts',
    example: 120,
    minimum: 1,
    required: false,
  })
  @IsNumber({}, { message: 'followUpIntervalMinutes must be a number' })
  @IsPositive({ message: 'followUpIntervalMinutes must be a positive number' })
  @IsOptional()
  readonly followUpIntervalMinutes?: number;

  @ApiProperty({
    description: 'Array of column names to include in portfolio exports',
    example: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
    type: [String],
    required: false,
  })
  @IsArray({ message: 'exportColumns must be an array' })
  @IsString({ each: true, message: 'Each export column must be a string' })
  @IsOptional()
  readonly exportColumns?: string[];

  @ApiProperty({
    description: 'Custom import configuration with YAML-based structure',
    type: CustomImportConfigDto,
    required: false,
  })
  @ValidateNested({ message: 'customImportConfig must be valid' })
  @Type(() => CustomImportConfigDto)
  @IsOptional()
  readonly customImportConfig?: CustomImportConfigDto;

  @ApiProperty({
    description: 'Array of workflow-specific statistics configurations for data extraction',
    type: [StatsConfigDto],
    required: false,
    isArray: true,
    examples: {
      multipleWorkflows: {
        summary: 'Multiple workflow configurations',
        value: [
          {
            workflowId: 'matrix-energia-negociador-divida-v1',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'middleware',
              path: '[\'valor-info\'].VALOR_ORIGINAL',
            },
          },
          {
            workflowId: 'matrix-energia-negociador-divida-v2',
            recoveredValueFrom: {
              source: 'middleware',
              path: '[\'deal-info\'].VALOR_RECUPERADO',
            },
            currentDebit: {
              source: 'customData',
              path: 'VALOR_CORRIGIDO',
            },
          },
        ],
      },
    },
  })
  @IsArray({ message: 'statsConfig must be an array' })
  @ValidateNested({ each: true, message: 'Each statsConfig item must be valid' })
  @Type(() => StatsConfigDto)
  @IsOptional()
  readonly statsConfig?: StatsConfigDto[];

  @ApiProperty({
    description: 'Legacy configuration for recovered value data source and extraction path (deprecated - use statsConfig instead)',
    type: StatsFieldConfigDto,
    required: false,
    deprecated: true,
    examples: {
      customData: {
        summary: 'Extract from portfolio item custom data',
        value: {
          source: 'customData',
          path: 'VALOR_RECEBIDO',
        },
      },
      middleware: {
        summary: 'Extract from middleware response',
        value: {
          source: 'middleware',
          path: '[\'deal-info\'].VALOR_RECUPERADO',
        },
      },
    },
  })
  @ValidateNested({ message: 'recoveredValueFrom must be valid' })
  @Type(() => StatsFieldConfigDto)
  @IsOptional()
  readonly recoveredValueFrom?: StatsFieldConfigDto;

  constructor(
    defaultWorkflowId?: string,
    timezoneUTC?: string,
    importCronExpression?: string,
    followUpWorkflowId?: string,
    followUpCronExpression?: string,
    followUpQuantity?: number,
    followUpIntervalMinutes?: number,
    exportColumns?: string[],
    customImportConfig?: CustomImportConfigDto,
    statsConfig?: StatsConfigDto[],
    recoveredValueFrom?: StatsFieldConfigDto,
  ) {
    this.defaultWorkflowId = defaultWorkflowId;
    this.timezoneUTC = timezoneUTC;
    this.importCronExpression = importCronExpression;
    this.followUpWorkflowId = followUpWorkflowId;
    this.followUpCronExpression = followUpCronExpression;
    this.followUpQuantity = followUpQuantity;
    this.followUpIntervalMinutes = followUpIntervalMinutes;
    this.exportColumns = exportColumns || [];
    this.customImportConfig = customImportConfig;
    this.statsConfig = statsConfig;
    this.recoveredValueFrom = recoveredValueFrom;
  }
}

export class CustomerPreferencesDto {
  @ApiProperty({
    description: 'UUID of the customer these preferences belong to',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
    required: false,
  })
  @IsUUID('4', { message: 'customerId must be a valid UUID' })
  @IsOptional()
  readonly customerId?: string;

  @ApiProperty({
    description: 'Portfolio-specific preferences and configurations',
    type: PortfolioPreferencesDto,
    required: false,
  })
  @ValidateNested({ message: 'portfolio preferences must be valid' })
  @Type(() => PortfolioPreferencesDto)
  @IsOptional()
  readonly portfolio?: PortfolioPreferencesDto;

  @IsEnum(RecordStatus, { message: 'status must be a valid RecordStatus' })
  @IsOptional()
  readonly status?: RecordStatus;

  constructor(data: Partial<CustomerPreferencesDto>) {
    Object.assign(this, data);
  }
}
