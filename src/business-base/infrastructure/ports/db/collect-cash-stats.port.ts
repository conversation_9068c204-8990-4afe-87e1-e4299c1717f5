import { DbCommonPort } from '@common/db/ports/common.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';

export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
  findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]>;
  findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]>;
  findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]>;
  findByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CollectCashStatsEntity[]>;
  findByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CollectCashStatsEntity[]>;
  getTotalRecoveredValueByCustomerId(customerId: string): Promise<number>;
  getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number>;
  getTotalRecoveredValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number>;
}
