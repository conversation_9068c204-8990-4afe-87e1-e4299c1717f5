import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { ExecuteWorkflow } from '@business-base/misc/interfaces/in/execute-workflow';
import { handleHttpError } from '@common/utils/handle-http-error';
import { WorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/workflow-response.dto';
import { ExecuteWorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/execute-workflow-response.dto';

@Injectable()
export class InfraWorkflowAdapter implements InfraWorkflowPort {
  private readonly orchestratorServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.orchestratorServiceUrl = String(process.env.ORCHESTRATOR_SERVICE_URL);

    if (!this.orchestratorServiceUrl || this.orchestratorServiceUrl === 'undefined') {
      throw new Error('ORCHESTRATOR_SERVICE_URL environment variable is not set');
    }

    logger.info('Workflow adapter initialized', {
      orchestratorServiceUrl: this.orchestratorServiceUrl,
      layer: 'WORKFLOW_ADAPTER',
    });
  }

  async startWorkflow(
    workflowId: string,
  ): Promise<{ workflowExecutionId: string; stepExecutionId: string; stepExecutionOrder: number }> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/start`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.post(url, { workflowId }, { headers }));

      return {
        workflowExecutionId: data?.data?.workflowExecutionId,
        stepExecutionId: data?.data?.stepExecutionId,
        stepExecutionOrder: data?.data?.stepExecutionOrder,
      };
    } catch (error) {
      handleHttpError(error, 'Infra-Workflow-adapter');
    }
  }

  async executeWorkflow(executeWorkflowData: ExecuteWorkflow): Promise<ExecuteWorkflowResponseDto> {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/execute`;
        logger.info(`Executing workflow - Attempt ${attempt}/${maxRetries}`, {
          url,
          workflowExecutionId: executeWorkflowData.workflowExecutionId,
          attempt,
          maxRetries,
        });

        const headers = {
          'Content-Type': 'application/json',
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=300, max=1000',
        };

        const workflowResponse = await lastValueFrom(
          this.httpService.post(url, executeWorkflowData, {
            headers,
            timeout: 300000, // 5 minutes
            // Add additional axios config for better connection handling
            maxRedirects: 5,
            validateStatus: (status) => status < 500, // Don't throw on 4xx errors
          }),
        );

        logger.info('Workflow executed successfully', {
          workflowExecutionId: executeWorkflowData.workflowExecutionId,
          attempt,
          statusCode: workflowResponse.status,
        });

        return workflowResponse.data.data;
      } catch (error) {
        const isLastAttempt = attempt === maxRetries;
        const isRetryableError = this.isRetryableError(error);

        logger.error('Execute Workflow error', {
          workflowExecutionId: executeWorkflowData.workflowExecutionId,
          attempt,
          maxRetries,
          isLastAttempt,
          isRetryableError,
          errorCode: error.code,
          errorMessage: error.message,
          statusCode: error?.response?.status,
          url: error?.config?.url,
        });

        if (isLastAttempt || !isRetryableError) {
          handleHttpError(error, 'infra-Workflow-adapter');
          return; // This won't be reached due to handleHttpError throwing
        }

        // Calculate exponential backoff delay
        const delay = baseDelay * Math.pow(2, attempt - 1);
        logger.info(`Retrying workflow execution in ${delay}ms`, {
          workflowExecutionId: executeWorkflowData.workflowExecutionId,
          attempt: attempt + 1,
          delay,
        });

        await this.sleep(delay);
      }
    }
  }

  private isRetryableError(error: any): boolean {
    // Network errors that are typically retryable
    const retryableCodes = [
      'ECONNRESET',
      'ECONNREFUSED',
      'ETIMEDOUT',
      'ECONNABORTED',
      'ENOTFOUND',
      'EAI_AGAIN',
    ];

    if (error.code && retryableCodes.includes(error.code)) {
      return true;
    }

    // HTTP status codes that are retryable
    const retryableStatusCodes = [502, 503, 504, 408, 429];
    if (error?.response?.status && retryableStatusCodes.includes(error.response.status)) {
      return true;
    }

    return false;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getWorkflowById(workflowId: string): Promise<WorkflowResponseDto> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/${workflowId}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };
      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));

      return data?.data;
    } catch (error) {
      handleHttpError(error, 'infra-Workflow-adapter:getWorkflowById');
    }
  }

  async getWorkflowByName(workflowName: string): Promise<WorkflowResponseDto> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/name/${workflowName}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };
      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));

      return data?.data;
    } catch (error) {
      handleHttpError(error, 'infra-Workflow-adapter:getWorkflowByName');
    }
  }

  async getWorkflowVariables(workflowId: string): Promise<string[]> {
    const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/${workflowId}/variables`;
    logger.info(`Posting data to ${url}`);
    const headers = {
      'Content-Type': 'application/json',
    };
    const { data } = await lastValueFrom(this.httpService.get(url, { headers }));
    return data?.data;
  }

  /**
   * Health check method to verify orchestrator service connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      const url = `${this.orchestratorServiceUrl}/health`;
      const response = await lastValueFrom(
        this.httpService.get(url, {
          timeout: 5000,
          headers: { 'Content-Type': 'application/json' }
        }),
      );

      logger.info('Orchestrator service health check passed', {
        url,
        statusCode: response.status,
        layer: 'WORKFLOW_ADAPTER',
      });

      return response.status === 200;
    } catch (error) {
      logger.error('Orchestrator service health check failed', {
        url: `${this.orchestratorServiceUrl}/health`,
        error: error.message,
        code: error.code,
        layer: 'WORKFLOW_ADAPTER',
      });

      return false;
    }
  }
}
