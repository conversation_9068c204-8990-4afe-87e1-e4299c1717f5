import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { ExecuteWorkflow } from '@business-base/misc/interfaces/in/execute-workflow';
import { handleHttpError } from '@common/utils/handle-http-error';
import { WorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/workflow-response.dto';
import { ExecuteWorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/execute-workflow-response.dto';

@Injectable()
export class InfraWorkflowAdapter implements InfraWorkflowPort {
  private readonly orchestratorServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.orchestratorServiceUrl = String(process.env.ORCHESTRATOR_SERVICE_URL);
  }

  async startWorkflow(
    workflowId: string,
  ): Promise<{ workflowExecutionId: string; stepExecutionId: string; stepExecutionOrder: number }> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/start`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.post(url, { workflowId }, { headers }));

      return {
        workflowExecutionId: data?.data?.workflowExecutionId,
        stepExecutionId: data?.data?.stepExecutionId,
        stepExecutionOrder: data?.data?.stepExecutionOrder,
      };
    } catch (error) {
      handleHttpError(error, 'Infra-Workflow-adapter');
    }
  }

  async executeWorkflow(executeWorkflowData: ExecuteWorkflow): Promise<ExecuteWorkflowResponseDto> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/execute`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const workflowResponse = await lastValueFrom(
        this.httpService.post(url, executeWorkflowData, { headers, timeout: 180000 }),
      );

      return workflowResponse.data.data;
    } catch (error) {
      logger.error(
        `Execute Workflow error:  ${executeWorkflowData} Error:${JSON.stringify(error)}`,
      );

      handleHttpError(error, 'infra-Workflow-adapter');
    }
  }

  async getWorkflowById(workflowId: string): Promise<WorkflowResponseDto> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/${workflowId}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };
      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));

      return data?.data;
    } catch (error) {
      handleHttpError(error, 'infra-Workflow-adapter:getWorkflowById');
    }
  }

  async getWorkflowByName(workflowName: string): Promise<WorkflowResponseDto> {
    try {
      const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/name/${workflowName}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };
      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));

      return data?.data;
    } catch (error) {
      handleHttpError(error, 'infra-Workflow-adapter:getWorkflowByName');
    }
  }

  async getWorkflowVariables(workflowId: string): Promise<string[]> {
    const url = `${this.orchestratorServiceUrl}/api/v1/orchestrator/workflows/${workflowId}/variables`;
    logger.info(`Posting data to ${url}`);
    const headers = {
      'Content-Type': 'application/json',
    };
    const { data } = await lastValueFrom(this.httpService.get(url, { headers }));
    return data?.data;
  }
}
