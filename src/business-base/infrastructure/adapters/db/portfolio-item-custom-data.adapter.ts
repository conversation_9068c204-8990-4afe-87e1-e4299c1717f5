import { Injectable } from '@nestjs/common';
import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { PortfolioItemCustomDataEntity } from '@business-base/domain/entities/portfolio-item-custom-data.entity';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';

@Injectable()
export class PortfolioItemCustomDataAdapter implements PortfolioItemCustomDataPort {
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_portfolio_items_custom_data';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async getByPortfolioItemId(
    id: string,
    portfolioItemId: string,
  ): Promise<PortfolioItemCustomDataEntity> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            portfolioItemId: portfolioItemId.toString(),
          },
        }),
      );

      return Item as PortfolioItemCustomDataEntity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while fetching portfolio item custom data for id: ${id}, portfolioItemId: ${portfolioItemId}. Error: ${error.message || error
          }. Type: ${error.constructor?.name || 'Unknown'}${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
          }`,
        error: error.message || error.toString(),
      });
    }
  }

  async create(entity: PortfolioItemCustomDataEntity): Promise<PortfolioItemCustomDataEntity> {
    try {
      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            portfolioItemId: entity.portfolioItemId,
            customData: entity.customData,
          },
        }),
      );
      return entity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while saving portfolio item custom data for id: ${entity.id
          }, portfolioItemId: ${entity.portfolioItemId}. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'
          }${error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''}`,
        error: error.message || error.toString(),
      });
    }
  }
}
