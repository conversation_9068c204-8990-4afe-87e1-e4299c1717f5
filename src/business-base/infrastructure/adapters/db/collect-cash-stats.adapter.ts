import { Injectable } from '@nestjs/common';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { RecordStatus } from '@common/enums';

@Injectable()
export class CollectCashStatsAdapter
  extends PrismaCommonAdapter<CollectCashStatsEntity>
  implements CollectCashStatsPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'collectCashStats');
  }

  async findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]> {
    const stats = await this.prisma.client.collectCashStats.findMany({
      where: {
        customerId,
        status: RecordStatus.ACTIVE,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async findByPortfolioId(portfolioId: string): Promise<CollectCashStatsEntity[]> {
    const stats = await this.prisma.client.collectCashStats.findMany({
      where: {
        portfolioId,
        status: RecordStatus.ACTIVE,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async findByPortfolioItemId(portfolioItemId: string): Promise<CollectCashStatsEntity[]> {
    const stats = await this.prisma.client.collectCashStats.findMany({
      where: {
        portfolioItemId,
        status: RecordStatus.ACTIVE,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async findByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CollectCashStatsEntity[]> {
    const whereClause: any = {
      customerId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const stats = await this.prisma.client.collectCashStats.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async findByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<CollectCashStatsEntity[]> {
    const whereClause: any = {
      portfolioId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const stats = await this.prisma.client.collectCashStats.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async getTotalRecoveredValueByCustomerId(customerId: string): Promise<number> {
    const result = await this.prisma.client.collectCashStats.aggregate({
      where: {
        customerId,
        status: RecordStatus.ACTIVE,
      },
      _sum: {
        valorRecuperado: true,
      },
    });

    return Number(result._sum.valorRecuperado) || 0;
  }

  async getTotalRecoveredValueByPortfolioId(portfolioId: string): Promise<number> {
    const result = await this.prisma.client.collectCashStats.aggregate({
      where: {
        portfolioId,
        status: RecordStatus.ACTIVE,
      },
      _sum: {
        valorRecuperado: true,
      },
    });

    return Number(result._sum.valorRecuperado) || 0;
  }

  async getTotalRecoveredValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      customerId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _sum: {
        valorRecuperado: true,
      },
    });

    return Number(result._sum.valorRecuperado) || 0;
  }

  private mapToEntity(stat: any): CollectCashStatsEntity {
    return new CollectCashStatsEntity(
      stat.id,
      stat.customerId,
      stat.portfolioId,
      stat.portfolioItemId,
      stat.workflowId,
      Number(stat.valorRecuperado),
      stat.quantidadeDeParcelas,
      stat.status as RecordStatus,
      stat.createdAt,
      stat.updatedAt,
    );
  }
}
