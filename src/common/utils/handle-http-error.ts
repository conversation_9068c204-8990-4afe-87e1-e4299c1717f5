import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { HttpStatus } from '@nestjs/common';
import { CorrelationContextService } from '@common/services/correlation-context.service';

export const handleHttpError = (error: any, name: string): void => {
  const traceId = CorrelationContextService.getTraceId();

  // Enhanced error logging with trace ID and structured format
  logger.error('HTTP error occurred', {
    traceId,
    source: name,
    error: error.message,
    errorCode: error.code,
    errorName: error.name,
    statusCode: error?.response?.status,
    statusText: error?.response?.statusText,
    url: error?.config?.url,
    method: error?.config?.method,
    timeout: error?.config?.timeout,
    responseData: error?.response?.data,
    requestData: typeof error?.config?.data === 'string' ?
      error.config.data.substring(0, 500) + '...' : // Truncate large payloads
      error?.config?.data,
    connectionInfo: {
      host: error?.config?.url ? new URL(error.config.url).host : 'unknown',
      port: error?.config?.url ? new URL(error.config.url).port : 'unknown',
    },
    timestamp: new Date().toISOString(),
    layer: 'HTTP_ERROR_HANDLER',
  });

  if (error?.response?.data) {
    let message = Array.isArray(error?.response?.data?.message)
      ? error?.response?.data?.message[0]
      : error?.response?.data?.message;

    // Ensure we always have a meaningful error message
    if (!message || message === null || message === undefined) {
      message =
        error?.response?.statusText ||
        error?.message ||
        `HTTP ${error?.response?.status} error` ||
        'Unknown HTTP error';
    }

    if (error?.response?.status === HttpStatus.NOT_FOUND) {
      throw new BusinessException(name, message, BusinessExceptionStatus.ITEM_NOT_FOUND);
    } else {
      throw new BusinessException(name, message, BusinessExceptionStatus.GENERAL_ERROR);
    }
  }

  // Handle specific network errors with more descriptive messages
  let fallbackMessage = error?.message || error?.code || error?.name || 'Network or connection error';

  // Provide more specific error messages for common network issues
  if (error?.code) {
    switch (error.code) {
      case 'ECONNRESET':
        fallbackMessage = 'Connection was reset by the server. This may indicate server overload, network issues, or timeout.';
        break;
      case 'ECONNREFUSED':
        fallbackMessage = 'Connection refused. The target service may be down or unreachable.';
        break;
      case 'ETIMEDOUT':
        fallbackMessage = 'Request timed out. The server took too long to respond.';
        break;
      case 'ENOTFOUND':
        fallbackMessage = 'DNS resolution failed. The hostname could not be resolved.';
        break;
      case 'ECONNABORTED':
        fallbackMessage = 'Connection was aborted. The request was cancelled or interrupted.';
        break;
      default:
        fallbackMessage = `Network error: ${error.code} - ${error.message || 'Unknown network issue'}`;
    }
  }

  throw new BusinessException(name, fallbackMessage, BusinessExceptionStatus.GENERAL_ERROR);
};
